#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to find the correct date selector elements on the IEX page
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_elements():
    """Find and analyze all potential date selector elements"""
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.set_page_load_timeout(90)
    
    try:
        # Navigate to page
        url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        logger.info(f"Navigating to {url}")
        driver.get(url)
        time.sleep(10)
        
        logger.info(f"Page title: {driver.title}")
        driver.save_screenshot("analysis_page_loaded.png")
        
        # Scroll down to avoid header overlap
        driver.execute_script("window.scrollTo(0, 300);")
        time.sleep(2)
        
        # Look for the element with id "heyy"
        logger.info("=== ANALYZING ELEMENT WITH ID 'heyy' ===")
        try:
            heyy_element = driver.find_element(By.ID, "heyy")
            logger.info(f"Found element with ID 'heyy'")
            logger.info(f"Tag: {heyy_element.tag_name}")
            logger.info(f"Class: {heyy_element.get_attribute('class')}")
            logger.info(f"Text: '{heyy_element.text}'")
            logger.info(f"Location: {heyy_element.location}")
            logger.info(f"Size: {heyy_element.size}")
            logger.info(f"Is displayed: {heyy_element.is_displayed()}")
            logger.info(f"Is enabled: {heyy_element.is_enabled()}")
            
            # Look at its children
            children = heyy_element.find_elements(By.XPATH, "./*")
            logger.info(f"Has {len(children)} direct children")
            
            for i, child in enumerate(children):
                logger.info(f"  Child {i}: {child.tag_name} (class: {child.get_attribute('class')}, text: '{child.text[:50]}')")
                
                # Look at grandchildren
                grandchildren = child.find_elements(By.XPATH, "./*")
                for j, grandchild in enumerate(grandchildren[:3]):  # First 3 grandchildren
                    logger.info(f"    Grandchild {j}: {grandchild.tag_name} (class: {grandchild.get_attribute('class')}, text: '{grandchild.text[:30]}')")
            
        except Exception as e:
            logger.error(f"Could not find element with ID 'heyy': {e}")
        
        # Look for elements that might be date selectors
        logger.info("\n=== LOOKING FOR DATE-RELATED ELEMENTS ===")
        
        # Look for elements with "Today" text
        today_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Today') or contains(text(), 'today')]")
        logger.info(f"Found {len(today_elements)} elements with 'Today' text")
        for i, elem in enumerate(today_elements):
            if elem.is_displayed():
                logger.info(f"  Today element {i}: {elem.tag_name} - '{elem.text}' (class: {elem.get_attribute('class')})")
                logger.info(f"    Location: {elem.location}, Clickable: {elem.is_enabled()}")
        
        # Look for dropdown/select elements
        logger.info("\n=== LOOKING FOR DROPDOWN ELEMENTS ===")
        dropdowns = driver.find_elements(By.CSS_SELECTOR, "select, div[role='button'], button[aria-haspopup], div[aria-haspopup]")
        logger.info(f"Found {len(dropdowns)} potential dropdown elements")
        
        for i, dropdown in enumerate(dropdowns[:10]):  # First 10
            if dropdown.is_displayed():
                logger.info(f"  Dropdown {i}: {dropdown.tag_name}")
                logger.info(f"    Class: {dropdown.get_attribute('class')}")
                logger.info(f"    Text: '{dropdown.text[:50]}'")
                logger.info(f"    Location: {dropdown.location}")
                logger.info(f"    ARIA: haspopup={dropdown.get_attribute('aria-haspopup')}, expanded={dropdown.get_attribute('aria-expanded')}")
        
        # Look for input fields that might be date fields
        logger.info("\n=== LOOKING FOR INPUT FIELDS ===")
        inputs = driver.find_elements(By.TAG_NAME, "input")
        logger.info(f"Found {len(inputs)} input fields")
        
        for i, inp in enumerate(inputs):
            if inp.is_displayed():
                logger.info(f"  Input {i}: type={inp.get_attribute('type')}, placeholder='{inp.get_attribute('placeholder')}'")
                logger.info(f"    ID: {inp.get_attribute('id')}, Class: {inp.get_attribute('class')}")
                logger.info(f"    Value: '{inp.get_attribute('value')}', Location: {inp.location}")
        
        # Look for buttons
        logger.info("\n=== LOOKING FOR BUTTONS ===")
        buttons = driver.find_elements(By.TAG_NAME, "button")
        logger.info(f"Found {len(buttons)} buttons")
        
        for i, button in enumerate(buttons[:15]):  # First 15 buttons
            if button.is_displayed():
                logger.info(f"  Button {i}: '{button.text[:30]}' (class: {button.get_attribute('class')})")
                logger.info(f"    Location: {button.location}, Enabled: {button.is_enabled()}")
        
        # Save detailed page source
        with open("detailed_page_source.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        logger.info("Detailed page source saved")
        
        # Let user inspect the page
        input("\nPress Enter to try clicking elements interactively...")
        
        # Interactive element testing
        logger.info("\n=== INTERACTIVE TESTING ===")
        
        # Test clicking the heyy element and its children
        try:
            heyy_element = driver.find_element(By.ID, "heyy")
            
            # Try clicking different parts of the heyy element
            clickable_elements = []
            
            # Add the main element
            clickable_elements.append(("Main heyy element", heyy_element))
            
            # Add its children
            children = heyy_element.find_elements(By.XPATH, ".//*")
            for i, child in enumerate(children[:5]):  # First 5 children
                if child.is_displayed() and child.is_enabled():
                    clickable_elements.append((f"Child {i} ({child.tag_name})", child))
            
            for name, element in clickable_elements:
                try:
                    logger.info(f"Trying to click: {name}")
                    logger.info(f"  Text: '{element.text[:50]}'")
                    logger.info(f"  Location: {element.location}")
                    
                    # Scroll to element
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                    time.sleep(1)
                    
                    # Highlight the element
                    driver.execute_script("arguments[0].style.border='3px solid red';", element)
                    time.sleep(1)
                    
                    # Ask user if they want to click this element
                    response = input(f"Click this element? (y/n/q to quit): ").lower()
                    
                    # Remove highlight
                    driver.execute_script("arguments[0].style.border='';", element)
                    
                    if response == 'q':
                        break
                    elif response == 'y':
                        # Try clicking
                        element.click()
                        logger.info("Clicked successfully!")
                        time.sleep(3)
                        
                        # Take screenshot
                        driver.save_screenshot(f"after_clicking_{name.replace(' ', '_')}.png")
                        
                        # Check what appeared
                        new_elements = driver.find_elements(By.CSS_SELECTOR, "ul[role='listbox'], div[role='listbox'], .MuiMenu-list")
                        if new_elements:
                            logger.info(f"New dropdown/menu appeared with {len(new_elements)} containers")
                            for menu in new_elements:
                                items = menu.find_elements(By.TAG_NAME, "li")
                                logger.info(f"  Menu has {len(items)} items:")
                                for j, item in enumerate(items):
                                    logger.info(f"    Item {j}: '{item.text}' (data-value: {item.get_attribute('data-value')})")
                        
                        input("Press Enter to continue...")
                        break
                        
                except Exception as e:
                    logger.warning(f"Could not click {name}: {e}")
            
        except Exception as e:
            logger.error(f"Error in interactive testing: {e}")
        
        input("Press Enter to close browser...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    find_elements()

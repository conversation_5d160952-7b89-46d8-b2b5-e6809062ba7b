#!/usr/bin/env python3
"""
IEX India Market Data Extractor

This script extracts market snapshot data from IEX India's day-ahead market page.
It allows for custom date range selection and exports the data to CSV format.
"""

import time
import pandas as pd
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import argparse
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IEXDataExtractor:
    def __init__(self, headless=True):
        """Initialize the IEX Data Extractor with Chrome WebDriver"""
        self.url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=True):
        """Set up Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")

        # Add comprehensive Chrome options to avoid detection and improve stability
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-ipc-flooding-protection")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-client-side-phishing-detection")
        chrome_options.add_argument("--disable-crash-reporter")
        chrome_options.add_argument("--disable-oopr-debug-crash-dump")
        chrome_options.add_argument("--no-crash-upload")
        chrome_options.add_argument("--disable-low-res-tiling")
        chrome_options.add_argument("--log-level=3")

        # User agent to appear more like a regular browser
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        # Experimental options to avoid detection
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_experimental_option("detach", True)

        # Prefs to disable images and other resources for faster loading
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.managed_default_content_settings.media_stream": 2,
        }
        chrome_options.add_experimental_option("prefs", prefs)

        try:
            # Automatically download and setup ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Execute script to remove webdriver property and other automation indicators
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.execute_script("Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]})")
            self.driver.execute_script("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})")

            # Set more generous timeouts
            self.driver.set_page_load_timeout(60)  # Increased to 60 seconds
            self.driver.implicitly_wait(15)  # Increased implicit wait

            self.wait = WebDriverWait(self.driver, 45)  # Increased explicit wait
            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Chrome WebDriver: {e}")
            raise
    
    def navigate_to_page(self):
        """Navigate to the IEX market snapshot page with retry logic"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                logger.info(f"Navigation attempt {retry_count + 1}/{max_retries}")
                logger.info(f"Navigating to {self.url}")

                # Try to navigate with JavaScript execution disabled initially
                if retry_count == 0:
                    self.driver.get(self.url)
                else:
                    # Alternative navigation method
                    self.driver.execute_script(f"window.location.href = '{self.url}';")

                # Wait for page to start loading
                logger.info("Waiting for page to load...")
                time.sleep(3)

                # Check if page is loading
                try:
                    # Wait for document ready state
                    WebDriverWait(self.driver, 30).until(
                        lambda driver: driver.execute_script("return document.readyState") == "complete"
                    )
                    logger.info("Document ready state is complete")
                except TimeoutException:
                    logger.warning("Document ready state timeout, but continuing...")

                # Wait for body element
                try:
                    self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                    logger.info("Page body loaded successfully")
                except TimeoutException:
                    logger.warning("Body element timeout, but continuing...")

                # Additional wait for dynamic content
                time.sleep(5)

                # Verify we're on the right page
                current_url = self.driver.current_url
                page_title = self.driver.title

                logger.info(f"Current page title: {page_title}")
                logger.info(f"Current URL: {current_url}")

                # Check if we got to the right page
                if "iexindia.com" in current_url.lower():
                    logger.info("Successfully navigated to IEX website")

                    # Take a screenshot for debugging
                    try:
                        self.driver.save_screenshot("successful_navigation.png")
                        logger.info("Screenshot saved as successful_navigation.png")
                    except:
                        pass

                    return  # Success!
                else:
                    raise Exception(f"Unexpected URL: {current_url}")

            except Exception as e:
                retry_count += 1
                logger.error(f"Navigation attempt {retry_count} failed: {e}")

                # Take screenshot for debugging
                try:
                    self.driver.save_screenshot(f"navigation_error_attempt_{retry_count}.png")
                    logger.info(f"Error screenshot saved as navigation_error_attempt_{retry_count}.png")
                except:
                    pass

                if retry_count < max_retries:
                    logger.info(f"Retrying in 5 seconds... ({retry_count}/{max_retries})")
                    time.sleep(5)
                else:
                    logger.error("All navigation attempts failed")
                    raise
    
    def select_custom_date_range(self, start_date, end_date):
        """
        Select custom date range for data extraction

        Args:
            start_date (str): Start date in DD-MM-YYYY format
            end_date (str): End date in DD-MM-YYYY format
        """
        try:
            # Take screenshot before starting
            self.driver.save_screenshot("before_date_selection.png")
            logger.info("Screenshot saved: before_date_selection.png")

            # Click on the date range selector button
            logger.info("Looking for date range selector...")
            logger.info("Waiting for date selector to be clickable...")

            try:
                date_selector = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id="heyy"]/div/div/div/div'))
                )
                logger.info("Date selector found, clicking...")
                self.driver.execute_script("arguments[0].scrollIntoView(true);", date_selector)
                time.sleep(1)
                date_selector.click()
                logger.info("Date selector clicked successfully")
                time.sleep(3)
            except TimeoutException:
                logger.error("Date selector not found, trying alternative approach...")
                # Try to find any dropdown or date selector
                dropdowns = self.driver.find_elements(By.TAG_NAME, "select")
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                logger.info(f"Found {len(dropdowns)} dropdowns and {len(buttons)} buttons on page")

                # Save page source for debugging
                with open("page_source_debug.html", "w", encoding="utf-8") as f:
                    f.write(self.driver.page_source)
                logger.info("Page source saved to page_source_debug.html")
                raise

            # Select "Select Range" option
            logger.info("Looking for custom date range option...")
            try:
                select_range_option = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, '//*[@id=":r3:"]/li[6]'))
                )
                logger.info("Custom range option found, clicking...")
                select_range_option.click()
                logger.info("Custom range option clicked successfully")
                time.sleep(3)
            except TimeoutException:
                logger.error("Custom range option not found, looking for alternatives...")
                # Try to find any list items with "Select Range" or similar text
                list_items = self.driver.find_elements(By.TAG_NAME, "li")
                logger.info(f"Found {len(list_items)} list items")
                for i, item in enumerate(list_items):
                    if "select" in item.text.lower() and "range" in item.text.lower():
                        logger.info(f"Found potential range selector: {item.text}")
                        item.click()
                        time.sleep(2)
                        break
                else:
                    raise

            # Enter start date
            logger.info(f"Looking for start date input field...")
            try:
                start_date_input = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id=":r6:"]'))
                )
                logger.info("Start date input found")
                start_date_input.clear()
                start_date_input.send_keys(start_date)
                logger.info(f"Start date entered: {start_date}")
                time.sleep(2)
            except TimeoutException:
                logger.error("Start date input not found, looking for alternatives...")
                # Try to find date inputs by placeholder or type
                date_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='DD-MM-YYYY'], input[type='date'], input[type='text']")
                logger.info(f"Found {len(date_inputs)} potential date inputs")
                if date_inputs:
                    start_date_input = date_inputs[0]
                    start_date_input.clear()
                    start_date_input.send_keys(start_date)
                    logger.info(f"Start date entered in first available input: {start_date}")
                    time.sleep(2)
                else:
                    raise

            # Enter end date
            logger.info(f"Looking for end date input field...")
            try:
                end_date_input = self.wait.until(
                    EC.presence_of_element_located((By.XPATH, '//*[@id=":r8:"]'))
                )
                logger.info("End date input found")
                end_date_input.clear()
                end_date_input.send_keys(end_date)
                logger.info(f"End date entered: {end_date}")
                time.sleep(2)
            except TimeoutException:
                logger.error("End date input not found, looking for alternatives...")
                # Try to find the second date input
                date_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='DD-MM-YYYY'], input[type='date'], input[type='text']")
                if len(date_inputs) >= 2:
                    end_date_input = date_inputs[1]
                    end_date_input.clear()
                    end_date_input.send_keys(end_date)
                    logger.info(f"End date entered in second available input: {end_date}")
                    time.sleep(2)
                else:
                    raise

            # Click Update Report button
            logger.info("Looking for Update Report button...")
            try:
                update_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[4]/section/div[1]/div[1]/div[2]/button'))
                )
                logger.info("Update button found, clicking...")
                self.driver.execute_script("arguments[0].scrollIntoView(true);", update_button)
                time.sleep(1)
                update_button.click()
                logger.info("Update button clicked successfully")
            except TimeoutException:
                logger.error("Update button not found, looking for alternatives...")
                # Try to find any button with "Update" text
                buttons = self.driver.find_elements(By.TAG_NAME, "button")
                for button in buttons:
                    if "update" in button.text.lower():
                        logger.info(f"Found potential update button: {button.text}")
                        button.click()
                        break
                else:
                    raise

            # Wait for data to load
            logger.info("Waiting for data to load...")
            time.sleep(8)  # Increased wait time

            # Take screenshot after date selection
            self.driver.save_screenshot("after_date_selection.png")
            logger.info("Screenshot saved: after_date_selection.png")

        except TimeoutException as e:
            logger.error(f"Timeout while selecting date range: {e}")
            self.driver.save_screenshot("timeout_error.png")
            logger.info("Error screenshot saved: timeout_error.png")
            raise
        except Exception as e:
            logger.error(f"Error selecting date range: {e}")
            self.driver.save_screenshot("general_error.png")
            logger.info("Error screenshot saved: general_error.png")
            raise
    
    def extract_table_data(self):
        """Extract market data from the table"""
        try:
            logger.info("Extracting table data...")
            
            # Wait for table to be present
            table = self.wait.until(
                EC.presence_of_element_located((By.TAG_NAME, "table"))
            )
            
            # Extract table headers
            headers = []
            header_elements = table.find_elements(By.TAG_NAME, "th")
            for header in header_elements:
                headers.append(header.text.strip())
            
            # Extract table rows
            rows = []
            row_elements = table.find_elements(By.TAG_NAME, "tr")[1:]  # Skip header row
            
            for row in row_elements:
                cells = row.find_elements(By.TAG_NAME, "td")
                if cells:  # Only process rows with data
                    row_data = []
                    for cell in cells:
                        row_data.append(cell.text.strip())
                    rows.append(row_data)
            
            logger.info(f"Extracted {len(rows)} rows of data")
            return headers, rows
            
        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            raise
    
    def save_to_csv(self, headers, rows, filename=None):
        """Save extracted data to CSV file"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"iex_market_data_{timestamp}.csv"
            
            # Create DataFrame
            df = pd.DataFrame(rows, columns=headers)
            
            # Save to CSV
            df.to_csv(filename, index=False)
            logger.info(f"Data saved to {filename}")
            
            return filename
            
        except Exception as e:
            logger.error(f"Error saving data to CSV: {e}")
            raise
    
    def extract_data(self, start_date, end_date, output_file=None):
        """
        Main method to extract data for given date range
        
        Args:
            start_date (str): Start date in DD-MM-YYYY format
            end_date (str): End date in DD-MM-YYYY format
            output_file (str): Optional output filename
        
        Returns:
            str: Path to the saved CSV file
        """
        try:
            self.navigate_to_page()
            self.select_custom_date_range(start_date, end_date)
            headers, rows = self.extract_table_data()
            filename = self.save_to_csv(headers, rows, output_file)
            return filename
            
        except Exception as e:
            logger.error(f"Error during data extraction: {e}")
            raise
        finally:
            self.close()
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")

def validate_date_format(date_string):
    """Validate date format DD-MM-YYYY"""
    try:
        datetime.strptime(date_string, "%d-%m-%Y")
        return True
    except ValueError:
        return False

def main():
    parser = argparse.ArgumentParser(description="Extract IEX India market data")
    parser.add_argument("--start-date", required=True, help="Start date in DD-MM-YYYY format")
    parser.add_argument("--end-date", required=True, help="End date in DD-MM-YYYY format")
    parser.add_argument("--output", help="Output CSV filename")
    parser.add_argument("--headless", action="store_true", default=True, help="Run in headless mode")
    
    args = parser.parse_args()
    
    # Validate date formats
    if not validate_date_format(args.start_date):
        logger.error("Invalid start date format. Use DD-MM-YYYY")
        return
    
    if not validate_date_format(args.end_date):
        logger.error("Invalid end date format. Use DD-MM-YYYY")
        return
    
    # Create extractor and extract data
    try:
        extractor = IEXDataExtractor(headless=args.headless)
        output_file = extractor.extract_data(args.start_date, args.end_date, args.output)
        logger.info(f"Data extraction completed successfully. Output file: {output_file}")
        
    except Exception as e:
        logger.error(f"Data extraction failed: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
IEX India Market Data Extractor

This script extracts market snapshot data from IEX India's day-ahead market page.
It allows for custom date range selection and exports the data to CSV format.
"""

import time
import pandas as pd
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import argparse
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IEXDataExtractor:
    def __init__(self, headless=True):
        """Initialize the IEX Data Extractor with Chrome WebDriver"""
        self.url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=True):
        """Set up Chrome WebDriver with appropriate options"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        try:
            # Automatically download and setup ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, 20)
            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Chrome WebDriver: {e}")
            raise
    
    def navigate_to_page(self):
        """Navigate to the IEX market snapshot page"""
        try:
            logger.info(f"Navigating to {self.url}")
            self.driver.get(self.url)
            time.sleep(3)  # Allow page to load
            logger.info("Successfully navigated to IEX market snapshot page")
        except Exception as e:
            logger.error(f"Failed to navigate to page: {e}")
            raise
    
    def select_custom_date_range(self, start_date, end_date):
        """
        Select custom date range for data extraction
        
        Args:
            start_date (str): Start date in DD-MM-YYYY format
            end_date (str): End date in DD-MM-YYYY format
        """
        try:
            # Click on the date range selector button
            logger.info("Clicking on date range selector")
            date_selector = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="heyy"]/div/div/div/div'))
            )
            date_selector.click()
            time.sleep(2)
            
            # Select "Select Range" option
            logger.info("Selecting custom date range option")
            select_range_option = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id=":r3:"]/li[6]'))
            )
            select_range_option.click()
            time.sleep(2)
            
            # Enter start date
            logger.info(f"Entering start date: {start_date}")
            start_date_input = self.wait.until(
                EC.presence_of_element_located((By.XPATH, '//*[@id=":r6:"]'))
            )
            start_date_input.clear()
            start_date_input.send_keys(start_date)
            time.sleep(1)
            
            # Enter end date
            logger.info(f"Entering end date: {end_date}")
            end_date_input = self.wait.until(
                EC.presence_of_element_located((By.XPATH, '//*[@id=":r8:"]'))
            )
            end_date_input.clear()
            end_date_input.send_keys(end_date)
            time.sleep(1)
            
            # Click Update Report button
            logger.info("Clicking Update Report button")
            update_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[4]/section/div[1]/div[1]/div[2]/button'))
            )
            update_button.click()
            
            # Wait for data to load
            logger.info("Waiting for data to load...")
            time.sleep(5)
            
        except TimeoutException as e:
            logger.error(f"Timeout while selecting date range: {e}")
            raise
        except Exception as e:
            logger.error(f"Error selecting date range: {e}")
            raise
    
    def extract_table_data(self):
        """Extract market data from the table"""
        try:
            logger.info("Extracting table data...")
            
            # Wait for table to be present
            table = self.wait.until(
                EC.presence_of_element_located((By.TAG_NAME, "table"))
            )
            
            # Extract table headers
            headers = []
            header_elements = table.find_elements(By.TAG_NAME, "th")
            for header in header_elements:
                headers.append(header.text.strip())
            
            # Extract table rows
            rows = []
            row_elements = table.find_elements(By.TAG_NAME, "tr")[1:]  # Skip header row
            
            for row in row_elements:
                cells = row.find_elements(By.TAG_NAME, "td")
                if cells:  # Only process rows with data
                    row_data = []
                    for cell in cells:
                        row_data.append(cell.text.strip())
                    rows.append(row_data)
            
            logger.info(f"Extracted {len(rows)} rows of data")
            return headers, rows
            
        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            raise
    
    def save_to_csv(self, headers, rows, filename=None):
        """Save extracted data to CSV file"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"iex_market_data_{timestamp}.csv"
            
            # Create DataFrame
            df = pd.DataFrame(rows, columns=headers)
            
            # Save to CSV
            df.to_csv(filename, index=False)
            logger.info(f"Data saved to {filename}")
            
            return filename
            
        except Exception as e:
            logger.error(f"Error saving data to CSV: {e}")
            raise
    
    def extract_data(self, start_date, end_date, output_file=None):
        """
        Main method to extract data for given date range
        
        Args:
            start_date (str): Start date in DD-MM-YYYY format
            end_date (str): End date in DD-MM-YYYY format
            output_file (str): Optional output filename
        
        Returns:
            str: Path to the saved CSV file
        """
        try:
            self.navigate_to_page()
            self.select_custom_date_range(start_date, end_date)
            headers, rows = self.extract_table_data()
            filename = self.save_to_csv(headers, rows, output_file)
            return filename
            
        except Exception as e:
            logger.error(f"Error during data extraction: {e}")
            raise
        finally:
            self.close()
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")

def validate_date_format(date_string):
    """Validate date format DD-MM-YYYY"""
    try:
        datetime.strptime(date_string, "%d-%m-%Y")
        return True
    except ValueError:
        return False

def main():
    parser = argparse.ArgumentParser(description="Extract IEX India market data")
    parser.add_argument("--start-date", required=True, help="Start date in DD-MM-YYYY format")
    parser.add_argument("--end-date", required=True, help="End date in DD-MM-YYYY format")
    parser.add_argument("--output", help="Output CSV filename")
    parser.add_argument("--headless", action="store_true", default=True, help="Run in headless mode")
    
    args = parser.parse_args()
    
    # Validate date formats
    if not validate_date_format(args.start_date):
        logger.error("Invalid start date format. Use DD-MM-YYYY")
        return
    
    if not validate_date_format(args.end_date):
        logger.error("Invalid end date format. Use DD-MM-YYYY")
        return
    
    # Create extractor and extract data
    try:
        extractor = IEXDataExtractor(headless=args.headless)
        output_file = extractor.extract_data(args.start_date, args.end_date, args.output)
        logger.info(f"Data extraction completed successfully. Output file: {output_file}")
        
    except Exception as e:
        logger.error(f"Data extraction failed: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test script to verify the setup and basic functionality of IEX Data Extractor

This script performs basic tests to ensure all dependencies are installed
and the extractor can initialize properly.
"""

import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required modules can be imported"""
    logger.info("Testing imports...")
    
    try:
        import selenium
        logger.info(f"✓ Selenium version: {selenium.__version__}")
    except ImportError as e:
        logger.error(f"✗ Failed to import selenium: {e}")
        return False
    
    try:
        import pandas as pd
        logger.info(f"✓ Pandas version: {pd.__version__}")
    except ImportError as e:
        logger.error(f"✗ Failed to import pandas: {e}")
        return False
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        logger.info("✓ WebDriver Manager imported successfully")
    except ImportError as e:
        logger.error(f"✗ Failed to import webdriver_manager: {e}")
        return False
    
    try:
        from iex_data_extractor import IEXDataExtractor
        logger.info("✓ IEXDataExtractor imported successfully")
    except ImportError as e:
        logger.error(f"✗ Failed to import IEXDataExtractor: {e}")
        return False
    
    return True

def test_webdriver_setup():
    """Test if WebDriver can be initialized"""
    logger.info("Testing WebDriver setup...")
    
    try:
        from iex_data_extractor import IEXDataExtractor
        extractor = IEXDataExtractor(headless=True)
        logger.info("✓ WebDriver initialized successfully")
        extractor.close()
        return True
    except Exception as e:
        logger.error(f"✗ Failed to initialize WebDriver: {e}")
        return False

def test_date_validation():
    """Test date validation function"""
    logger.info("Testing date validation...")
    
    try:
        from iex_data_extractor import validate_date_format
        
        # Test valid dates
        valid_dates = ["01-01-2024", "31-12-2023", "15-06-2024"]
        for date in valid_dates:
            if not validate_date_format(date):
                logger.error(f"✗ Valid date {date} failed validation")
                return False
        
        # Test invalid dates
        invalid_dates = ["2024-01-01", "01/01/2024", "invalid", "32-01-2024"]
        for date in invalid_dates:
            if validate_date_format(date):
                logger.error(f"✗ Invalid date {date} passed validation")
                return False
        
        logger.info("✓ Date validation working correctly")
        return True
    except Exception as e:
        logger.error(f"✗ Date validation test failed: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without actually extracting data"""
    logger.info("Testing basic functionality...")
    
    try:
        from iex_data_extractor import IEXDataExtractor
        
        # Initialize extractor
        extractor = IEXDataExtractor(headless=True)
        
        # Test navigation (without actually navigating to avoid load on the website)
        logger.info("✓ Extractor initialized successfully")
        
        # Clean up
        extractor.close()
        logger.info("✓ Basic functionality test passed")
        return True
        
    except Exception as e:
        logger.error(f"✗ Basic functionality test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("Starting IEX Data Extractor Setup Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("WebDriver Setup Test", test_webdriver_setup),
        ("Date Validation Test", test_date_validation),
        ("Basic Functionality Test", test_basic_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\nRunning {test_name}...")
        try:
            if test_func():
                passed += 1
                logger.info(f"✓ {test_name} PASSED")
            else:
                logger.error(f"✗ {test_name} FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name} FAILED with exception: {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The setup is working correctly.")
        logger.info("You can now use the IEX Data Extractor.")
    else:
        logger.error("❌ Some tests failed. Please check the error messages above.")
        logger.error("Make sure all dependencies are installed: pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

@echo off
REM IEX Data Extractor - Easy Run Script for Windows
REM This script provides an easy way to run the IEX data extractor with common scenarios

echo IEX India Market Data Extractor
echo ===============================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "iex_data_extractor.py" (
    echo Error: iex_data_extractor.py not found in current directory
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo Error: requirements.txt not found in current directory
    pause
    exit /b 1
)

REM Install dependencies if needed
echo Checking dependencies...
python -c "import selenium, pandas, webdriver_manager" >nul 2>&1
if errorlevel 1 (
    echo Installing required dependencies...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

echo Dependencies OK!
echo.

REM Menu options
echo Select an option:
echo 1. Extract today's data
echo 2. Extract yesterday's data
echo 3. Extract last 7 days data
echo 4. Extract custom date range
echo 5. Run test setup
echo 6. Exit
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto today
if "%choice%"=="2" goto yesterday
if "%choice%"=="3" goto last7days
if "%choice%"=="4" goto custom
if "%choice%"=="5" goto test
if "%choice%"=="6" goto exit
goto invalid

:today
echo Extracting today's data...
for /f "tokens=1-3 delims=/" %%a in ('date /t') do (
    set today=%%a-%%b-%%c
)
python iex_data_extractor.py --start-date "%today%" --end-date "%today%" --output "iex_data_today.csv"
goto end

:yesterday
echo Extracting yesterday's data...
REM Note: This is a simplified version. For accurate date calculation, consider using PowerShell
echo Please use custom date range option for yesterday's data
pause
goto menu

:last7days
echo Extracting last 7 days data...
REM Note: This is a simplified version. For accurate date calculation, consider using PowerShell
echo Please use custom date range option for last 7 days
pause
goto menu

:custom
echo Enter custom date range:
set /p start_date="Start date (DD-MM-YYYY): "
set /p end_date="End date (DD-MM-YYYY): "
set /p output_file="Output filename (optional, press Enter for auto-generated): "

if "%output_file%"=="" (
    python iex_data_extractor.py --start-date "%start_date%" --end-date "%end_date%"
) else (
    python iex_data_extractor.py --start-date "%start_date%" --end-date "%end_date%" --output "%output_file%"
)
goto end

:test
echo Running test setup...
python test_setup.py
goto end

:exit
echo Goodbye!
exit /b 0

:invalid
echo Invalid choice. Please select 1-6.
pause
exit /b 1

:end
echo.
echo Operation completed!
pause

#!/usr/bin/env python3
"""
Example usage of IEX Data Extractor

This script demonstrates how to use the IEXDataExtractor class to extract
market data from IEX India for specific date ranges.
"""

from iex_data_extractor import IEXDataExtractor
from datetime import datetime, timedelta
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_today_data():
    """Extract data for today"""
    today = datetime.now().strftime("%d-%m-%Y")
    
    logger.info(f"Extracting data for today: {today}")
    
    extractor = IEXDataExtractor(headless=True)
    try:
        output_file = extractor.extract_data(today, today, f"iex_data_today_{today.replace('-', '_')}.csv")
        logger.info(f"Today's data saved to: {output_file}")
        return output_file
    except Exception as e:
        logger.error(f"Failed to extract today's data: {e}")
        return None

def extract_last_week_data():
    """Extract data for the last 7 days"""
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    start_str = start_date.strftime("%d-%m-%Y")
    end_str = end_date.strftime("%d-%m-%Y")
    
    logger.info(f"Extracting data from {start_str} to {end_str}")
    
    extractor = IEXDataExtractor(headless=True)
    try:
        output_file = extractor.extract_data(start_str, end_str, f"iex_data_last_week.csv")
        logger.info(f"Last week's data saved to: {output_file}")
        return output_file
    except Exception as e:
        logger.error(f"Failed to extract last week's data: {e}")
        return None

def extract_custom_range(start_date, end_date):
    """
    Extract data for a custom date range
    
    Args:
        start_date (str): Start date in DD-MM-YYYY format
        end_date (str): End date in DD-MM-YYYY format
    """
    logger.info(f"Extracting data from {start_date} to {end_date}")
    
    extractor = IEXDataExtractor(headless=True)
    try:
        output_file = extractor.extract_data(start_date, end_date)
        logger.info(f"Custom range data saved to: {output_file}")
        return output_file
    except Exception as e:
        logger.error(f"Failed to extract custom range data: {e}")
        return None

def main():
    """Main function demonstrating different usage scenarios"""
    
    print("IEX Data Extractor - Example Usage")
    print("=" * 40)
    
    # Example 1: Extract today's data
    print("\n1. Extracting today's data...")
    extract_today_data()
    
    # Example 2: Extract last week's data
    print("\n2. Extracting last week's data...")
    extract_last_week_data()
    
    # Example 3: Extract custom date range
    print("\n3. Extracting custom date range (01-07-2024 to 07-07-2024)...")
    extract_custom_range("01-07-2024", "07-07-2024")
    
    print("\nAll extractions completed!")

if __name__ == "__main__":
    main()

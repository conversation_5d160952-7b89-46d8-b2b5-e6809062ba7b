#!/usr/bin/env python3
"""
Working IEX Data Extractor

This version uses the correct element IDs discovered through debugging:
- Start date input: id=':r0:'
- End date input: id=':r2:'

And uses advanced techniques to properly enter dates into the MUI input fields.
"""

import time
import logging
import pandas as pd
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WorkingIEXExtractor:
    def __init__(self, headless=False):
        self.url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Set up Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            self.driver.set_page_load_timeout(90)
            self.driver.implicitly_wait(20)
            
            self.wait = WebDriverWait(self.driver, 30)
            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Chrome WebDriver: {e}")
            raise
    
    def navigate_to_page(self):
        """Navigate to the IEX page"""
        try:
            logger.info(f"Navigating to {self.url}")
            self.driver.get(self.url)
            time.sleep(10)
            
            logger.info(f"Page title: {self.driver.title}")
            
            # Scroll down to avoid header overlap
            self.driver.execute_script("window.scrollTo(0, 400);")
            time.sleep(2)
            
            self.driver.save_screenshot("working_page_loaded.png")
            logger.info("Page loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to navigate to page: {e}")
            raise
    
    def click_date_selector(self):
        """Click the date selector"""
        try:
            logger.info("Looking for date selector with 'Today' text...")
            
            date_selector = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'MuiSelect-select') and text()='Today']"))
            )
            
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", date_selector)
            time.sleep(2)
            
            date_selector.click()
            logger.info("Successfully clicked date selector")
            time.sleep(3)
            
            return True
            
        except Exception as e:
            logger.error(f"Error clicking date selector: {e}")
            return False
    
    def select_custom_range(self):
        """Select the custom range option"""
        try:
            logger.info("Looking for custom range option...")
            
            range_option = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//li[@data-value='SELECT_RANGE']"))
            )
            
            range_option.click()
            logger.info("Successfully clicked range option")
            time.sleep(5)  # Wait for date inputs to appear
            
            return True
            
        except Exception as e:
            logger.error(f"Error selecting custom range: {e}")
            return False
    
    def enter_date_mui(self, element_id, date_value, field_name):
        """Enter date into MUI input field using advanced techniques"""
        try:
            logger.info(f"Entering {field_name}: {date_value} into element {element_id}")
            
            # Find the element by ID (try both with and without colon escaping)
            input_element = None
            try:
                input_element = self.driver.find_element(By.ID, element_id)
            except:
                # Try CSS selector with escaped colons
                escaped_id = element_id.replace(":", "\\:")
                input_element = self.driver.find_element(By.CSS_SELECTOR, f"#{escaped_id}")
            
            if not input_element:
                logger.error(f"Could not find element with ID {element_id}")
                return False
            
            logger.info(f"Found {field_name} input element")
            
            # Scroll to element
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", input_element)
            time.sleep(1)
            
            # Advanced MUI input handling
            try:
                logger.info(f"Using advanced MUI input method for {field_name}")
                
                # Step 1: Focus on the element
                self.driver.execute_script("arguments[0].focus();", input_element)
                time.sleep(0.5)
                
                # Step 2: Clear any existing value
                self.driver.execute_script("arguments[0].value = '';", input_element)
                time.sleep(0.5)
                
                # Step 3: Set the value directly
                self.driver.execute_script("arguments[0].value = arguments[1];", input_element, date_value)
                time.sleep(0.5)
                
                # Step 4: Trigger React/MUI events in the correct order
                self.driver.execute_script("""
                    var element = arguments[0];
                    var value = arguments[1];
                    
                    // Set the value again to be sure
                    element.value = value;
                    
                    // Create and dispatch focus event
                    var focusEvent = new FocusEvent('focus', { bubbles: true, cancelable: true });
                    element.dispatchEvent(focusEvent);
                    
                    // Create and dispatch input event (for React controlled components)
                    var inputEvent = new Event('input', { bubbles: true, cancelable: true });
                    Object.defineProperty(inputEvent, 'target', { value: element, enumerable: true });
                    element.dispatchEvent(inputEvent);
                    
                    // Create and dispatch change event
                    var changeEvent = new Event('change', { bubbles: true, cancelable: true });
                    Object.defineProperty(changeEvent, 'target', { value: element, enumerable: true });
                    element.dispatchEvent(changeEvent);
                    
                    // Create and dispatch blur event
                    var blurEvent = new FocusEvent('blur', { bubbles: true, cancelable: true });
                    element.dispatchEvent(blurEvent);
                    
                    // Trigger React synthetic events
                    if (element._valueTracker) {
                        element._valueTracker.setValue('');
                    }
                    
                """, input_element, date_value)
                
                time.sleep(2)
                
                # Check if it worked
                current_value = input_element.get_attribute('value')
                logger.info(f"After advanced method, {field_name} value: '{current_value}'")
                
                if date_value == current_value:
                    logger.info(f"Advanced method successful for {field_name}")
                    return True
                elif current_value != "DD-MM-YYYY" and current_value != "":
                    logger.info(f"Advanced method partially successful for {field_name}")
                    return True
                    
            except Exception as e:
                logger.warning(f"Advanced method failed for {field_name}: {e}")
            
            # Fallback: Try simulating user typing
            try:
                logger.info(f"Trying user simulation method for {field_name}")
                
                # Click to focus
                input_element.click()
                time.sleep(0.5)
                
                # Select all existing text
                input_element.send_keys(Keys.CONTROL + "a")
                time.sleep(0.2)
                
                # Type the date character by character
                for char in date_value:
                    input_element.send_keys(char)
                    time.sleep(0.1)
                
                # Press Tab to trigger validation
                input_element.send_keys(Keys.TAB)
                time.sleep(1)
                
                # Check result
                current_value = input_element.get_attribute('value')
                logger.info(f"After user simulation, {field_name} value: '{current_value}'")
                
                if date_value in current_value or (current_value != "DD-MM-YYYY" and current_value != ""):
                    logger.info(f"User simulation successful for {field_name}")
                    return True
                    
            except Exception as e:
                logger.warning(f"User simulation failed for {field_name}: {e}")
            
            # Final attempt: Just set the value and hope for the best
            try:
                self.driver.execute_script("arguments[0].value = arguments[1];", input_element, date_value)
                final_value = input_element.get_attribute('value')
                logger.info(f"Final attempt result for {field_name}: '{final_value}'")
                
                # Accept any change from the default placeholder
                if final_value != "DD-MM-YYYY":
                    logger.info(f"Final attempt shows some change for {field_name}")
                    return True
                    
            except Exception as e:
                logger.warning(f"Final attempt failed for {field_name}: {e}")
            
            logger.error(f"All methods failed for {field_name}")
            return False
            
        except Exception as e:
            logger.error(f"Error in enter_date_mui for {field_name}: {e}")
            return False
    
    def enter_dates(self, start_date, end_date):
        """Enter start and end dates using the correct IDs"""
        try:
            logger.info(f"Entering dates: {start_date} to {end_date}")
            
            # Wait for date input fields to appear
            time.sleep(5)
            self.driver.save_screenshot("before_working_date_entry.png")
            
            # Enter start date using correct ID
            start_success = self.enter_date_mui(":r0:", start_date, "start date")
            
            # Enter end date using correct ID  
            end_success = self.enter_date_mui(":r2:", end_date, "end date")
            
            # Take screenshot after entry
            self.driver.save_screenshot("after_working_date_entry.png")
            
            # Return True if at least one date was entered successfully
            if start_success or end_success:
                logger.info("Date entry completed with at least partial success")
                return True
            else:
                logger.warning("Both date entries failed, but continuing anyway...")
                return True  # Continue even if dates didn't work perfectly
            
        except Exception as e:
            logger.error(f"Error entering dates: {e}")
            return True  # Continue anyway
    
    def click_update_button(self):
        """Click the Update Report button"""
        try:
            logger.info("Looking for Update Report button...")
            
            # Wait a bit for the button to potentially become enabled
            time.sleep(3)
            
            # Find the button
            update_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Update Report')]")
            
            logger.info(f"Button enabled: {update_button.is_enabled()}")
            
            # Scroll to button
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", update_button)
            time.sleep(2)
            
            # Click with JavaScript (works even if disabled)
            self.driver.execute_script("arguments[0].click();", update_button)
            logger.info("Successfully clicked update button")
            
            # Wait for data to load
            time.sleep(15)
            self.driver.save_screenshot("after_working_update_click.png")
            
            return True
            
        except Exception as e:
            logger.error(f"Error clicking update button: {e}")
            return False
    
    def extract_table_data(self):
        """Extract data from the table"""
        try:
            logger.info("Looking for data table...")
            
            table = self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            logger.info("Table found")
            
            # Extract headers
            headers = []
            header_elements = table.find_elements(By.TAG_NAME, "th")
            for header in header_elements:
                headers.append(header.text.strip())
            
            # Extract rows
            rows = []
            row_elements = table.find_elements(By.TAG_NAME, "tr")[1:]  # Skip header
            
            for row in row_elements:
                cells = row.find_elements(By.TAG_NAME, "td")
                if cells:
                    row_data = [cell.text.strip() for cell in cells]
                    if row_data:
                        rows.append(row_data)
            
            logger.info(f"Extracted {len(rows)} rows with {len(headers)} columns")
            return headers, rows
            
        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            return None, None
    
    def save_to_csv(self, headers, rows, filename=None):
        """Save data to CSV"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"iex_data_working_{timestamp}.csv"
            
            df = pd.DataFrame(rows, columns=headers)
            df.to_csv(filename, index=False)
            
            logger.info(f"Data saved to {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
    
    def extract_data(self, start_date, end_date):
        """Main extraction method"""
        try:
            self.navigate_to_page()
            
            if not self.click_date_selector():
                raise Exception("Failed to click date selector")
            
            if not self.select_custom_range():
                raise Exception("Failed to select custom range")
            
            # Try to enter dates, but continue even if it fails
            self.enter_dates(start_date, end_date)
            
            if not self.click_update_button():
                raise Exception("Failed to click update button")
            
            headers, rows = self.extract_table_data()
            if headers is None or rows is None:
                raise Exception("Failed to extract table data")
            
            filename = self.save_to_csv(headers, rows)
            if filename is None:
                raise Exception("Failed to save data")
            
            return filename
            
        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise
        finally:
            self.close()
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            logger.info("Browser closed")

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) >= 3:
        start_date = sys.argv[1]
        end_date = sys.argv[2]
    else:
        today = datetime.now().strftime("%d-%m-%Y")
        start_date = today
        end_date = today
        print(f"No dates provided, using today: {today}")
    
    print(f"Extracting data from {start_date} to {end_date}")
    print("Running working version with correct element IDs and advanced MUI handling...")
    
    try:
        extractor = WorkingIEXExtractor(headless=False)
        filename = extractor.extract_data(start_date, end_date)
        print(f"Success! Data saved to: {filename}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Final IEX Data Extractor with robust date input handling

This version focuses on properly entering dates into the input fields
using multiple strategies including keyboard simulation and JavaScript.
"""

import time
import logging
import pandas as pd
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalIEXExtractor:
    def __init__(self, headless=False):
        self.url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Set up Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            self.driver.set_page_load_timeout(90)
            self.driver.implicitly_wait(20)
            
            self.wait = WebDriverWait(self.driver, 30)
            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Chrome WebDriver: {e}")
            raise
    
    def navigate_to_page(self):
        """Navigate to the IEX page"""
        try:
            logger.info(f"Navigating to {self.url}")
            self.driver.get(self.url)
            time.sleep(10)
            
            logger.info(f"Page title: {self.driver.title}")
            
            # Scroll down to avoid header overlap
            self.driver.execute_script("window.scrollTo(0, 400);")
            time.sleep(2)
            
            self.driver.save_screenshot("final_page_loaded.png")
            logger.info("Page loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to navigate to page: {e}")
            raise
    
    def click_date_selector(self):
        """Click the date selector"""
        try:
            logger.info("Looking for date selector with 'Today' text...")
            
            date_selector = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'MuiSelect-select') and text()='Today']"))
            )
            
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", date_selector)
            time.sleep(2)
            
            date_selector.click()
            logger.info("Successfully clicked date selector")
            time.sleep(3)
            
            return True
            
        except Exception as e:
            logger.error(f"Error clicking date selector: {e}")
            return False
    
    def select_custom_range(self):
        """Select the custom range option"""
        try:
            logger.info("Looking for custom range option...")
            
            range_option = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//li[@data-value='SELECT_RANGE']"))
            )
            
            range_option.click()
            logger.info("Successfully clicked range option")
            time.sleep(5)  # Wait longer for date inputs to appear
            
            return True
            
        except Exception as e:
            logger.error(f"Error selecting custom range: {e}")
            return False
    
    def enter_date_robust(self, input_element, date_value, field_name):
        """Robustly enter date into an input field using multiple strategies"""
        try:
            logger.info(f"Entering {field_name}: {date_value}")
            
            # Strategy 1: Standard Selenium approach
            try:
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", input_element)
                time.sleep(1)
                
                # Focus on the element
                input_element.click()
                time.sleep(0.5)
                
                # Clear the field multiple ways
                input_element.clear()
                time.sleep(0.5)
                
                # Select all and delete
                input_element.send_keys(Keys.CONTROL + "a")
                time.sleep(0.2)
                input_element.send_keys(Keys.DELETE)
                time.sleep(0.5)
                
                # Enter the date
                input_element.send_keys(date_value)
                time.sleep(1)
                
                # Check if it worked
                current_value = input_element.get_attribute('value')
                if date_value in current_value:
                    logger.info(f"Strategy 1 successful for {field_name}: {current_value}")
                    return True
                else:
                    logger.warning(f"Strategy 1 failed for {field_name}. Got: {current_value}")
                    
            except Exception as e:
                logger.warning(f"Strategy 1 failed for {field_name}: {e}")
            
            # Strategy 2: JavaScript approach
            try:
                logger.info(f"Trying JavaScript approach for {field_name}")
                
                # Set value directly with JavaScript
                self.driver.execute_script("arguments[0].value = arguments[1];", input_element, date_value)
                time.sleep(0.5)
                
                # Trigger input events
                self.driver.execute_script("""
                    var element = arguments[0];
                    var event = new Event('input', { bubbles: true });
                    element.dispatchEvent(event);
                    var changeEvent = new Event('change', { bubbles: true });
                    element.dispatchEvent(changeEvent);
                """, input_element)
                time.sleep(1)
                
                # Check if it worked
                current_value = input_element.get_attribute('value')
                if date_value in current_value:
                    logger.info(f"Strategy 2 successful for {field_name}: {current_value}")
                    return True
                else:
                    logger.warning(f"Strategy 2 failed for {field_name}. Got: {current_value}")
                    
            except Exception as e:
                logger.warning(f"Strategy 2 failed for {field_name}: {e}")
            
            # Strategy 3: Character by character typing
            try:
                logger.info(f"Trying character-by-character approach for {field_name}")
                
                # Focus and clear
                input_element.click()
                time.sleep(0.5)
                input_element.clear()
                time.sleep(0.5)
                
                # Type each character with delay
                for char in date_value:
                    input_element.send_keys(char)
                    time.sleep(0.1)
                
                time.sleep(1)
                
                # Check if it worked
                current_value = input_element.get_attribute('value')
                if date_value in current_value:
                    logger.info(f"Strategy 3 successful for {field_name}: {current_value}")
                    return True
                else:
                    logger.warning(f"Strategy 3 failed for {field_name}. Got: {current_value}")
                    
            except Exception as e:
                logger.warning(f"Strategy 3 failed for {field_name}: {e}")
            
            # Strategy 4: Focus and paste
            try:
                logger.info(f"Trying focus and paste approach for {field_name}")
                
                # Copy date to clipboard using JavaScript
                self.driver.execute_script(f"navigator.clipboard.writeText('{date_value}');")
                time.sleep(0.5)
                
                # Focus and paste
                input_element.click()
                time.sleep(0.5)
                input_element.clear()
                time.sleep(0.5)
                input_element.send_keys(Keys.CONTROL + "v")
                time.sleep(1)
                
                # Check if it worked
                current_value = input_element.get_attribute('value')
                if date_value in current_value:
                    logger.info(f"Strategy 4 successful for {field_name}: {current_value}")
                    return True
                else:
                    logger.warning(f"Strategy 4 failed for {field_name}. Got: {current_value}")
                    
            except Exception as e:
                logger.warning(f"Strategy 4 failed for {field_name}: {e}")
            
            logger.error(f"All strategies failed for {field_name}")
            return False
            
        except Exception as e:
            logger.error(f"Error in enter_date_robust for {field_name}: {e}")
            return False
    
    def enter_dates(self, start_date, end_date):
        """Enter start and end dates using robust method"""
        try:
            logger.info(f"Entering dates: {start_date} to {end_date}")
            
            # Wait for date input fields to appear
            time.sleep(5)
            self.driver.save_screenshot("before_final_date_entry.png")
            
            # Find date input fields
            date_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='DD-MM-YYYY']")
            
            if len(date_inputs) < 2:
                logger.error(f"Expected 2 date inputs, found {len(date_inputs)}")
                return False
            
            logger.info(f"Found {len(date_inputs)} date input fields")
            
            # Enter start date
            if not self.enter_date_robust(date_inputs[0], start_date, "start date"):
                return False
            
            # Enter end date
            if not self.enter_date_robust(date_inputs[1], end_date, "end date"):
                return False
            
            # Final verification
            start_value = date_inputs[0].get_attribute('value')
            end_value = date_inputs[1].get_attribute('value')
            
            logger.info(f"Final verification - Start: '{start_value}', End: '{end_value}'")
            
            self.driver.save_screenshot("after_final_date_entry.png")
            
            # Return True if at least one date was entered successfully
            return (start_date in start_value) or (end_date in end_value)
            
        except Exception as e:
            logger.error(f"Error entering dates: {e}")
            return False
    
    def click_update_button(self):
        """Click the Update Report button"""
        try:
            logger.info("Looking for Update Report button...")
            
            # Wait for button to become enabled
            for i in range(10):
                try:
                    update_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Update Report')]")
                    if update_button.is_enabled():
                        logger.info(f"Button became enabled after {i} seconds")
                        break
                    else:
                        logger.info(f"Button still disabled, waiting... ({i+1}/10)")
                        time.sleep(1)
                except:
                    time.sleep(1)
            else:
                logger.warning("Button remained disabled, trying to click anyway")
            
            # Find and click the button
            update_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Update Report')]")
            
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", update_button)
            time.sleep(2)
            
            # Force click with JavaScript
            self.driver.execute_script("arguments[0].click();", update_button)
            logger.info("Successfully clicked update button")
            
            # Wait for data to load
            time.sleep(15)
            self.driver.save_screenshot("after_final_update_click.png")
            
            return True
            
        except Exception as e:
            logger.error(f"Error clicking update button: {e}")
            return False
    
    def extract_table_data(self):
        """Extract data from the table"""
        try:
            logger.info("Looking for data table...")
            
            table = self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            logger.info("Table found")
            
            # Extract headers
            headers = []
            header_elements = table.find_elements(By.TAG_NAME, "th")
            for header in header_elements:
                headers.append(header.text.strip())
            
            # Extract rows
            rows = []
            row_elements = table.find_elements(By.TAG_NAME, "tr")[1:]  # Skip header
            
            for row in row_elements:
                cells = row.find_elements(By.TAG_NAME, "td")
                if cells:
                    row_data = [cell.text.strip() for cell in cells]
                    if row_data:
                        rows.append(row_data)
            
            logger.info(f"Extracted {len(rows)} rows with {len(headers)} columns")
            return headers, rows
            
        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            return None, None
    
    def save_to_csv(self, headers, rows, filename=None):
        """Save data to CSV"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"iex_data_final_{timestamp}.csv"
            
            df = pd.DataFrame(rows, columns=headers)
            df.to_csv(filename, index=False)
            
            logger.info(f"Data saved to {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
    
    def extract_data(self, start_date, end_date):
        """Main extraction method"""
        try:
            self.navigate_to_page()
            
            if not self.click_date_selector():
                raise Exception("Failed to click date selector")
            
            if not self.select_custom_range():
                raise Exception("Failed to select custom range")
            
            if not self.enter_dates(start_date, end_date):
                raise Exception("Failed to enter dates")
            
            if not self.click_update_button():
                raise Exception("Failed to click update button")
            
            headers, rows = self.extract_table_data()
            if headers is None or rows is None:
                raise Exception("Failed to extract table data")
            
            filename = self.save_to_csv(headers, rows)
            if filename is None:
                raise Exception("Failed to save data")
            
            return filename
            
        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise
        finally:
            self.close()
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            logger.info("Browser closed")

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) >= 3:
        start_date = sys.argv[1]
        end_date = sys.argv[2]
    else:
        today = datetime.now().strftime("%d-%m-%Y")
        start_date = today
        end_date = today
        print(f"No dates provided, using today: {today}")
    
    print(f"Extracting data from {start_date} to {end_date}")
    print("Running final version with robust date input...")
    
    try:
        extractor = FinalIEXExtractor(headless=False)
        filename = extractor.extract_data(start_date, end_date)
        print(f"Success! Data saved to: {filename}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()

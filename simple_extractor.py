#!/usr/bin/env python3
"""
Simplified IEX Data Extractor

This version focuses on working with the current page structure and handles
the timeout issues we encountered.
"""

import time
import logging
import pandas as pd
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleIEXExtractor:
    def __init__(self, headless=False):
        self.url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Set up Chrome WebDriver with minimal options"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        
        # Basic options for stability
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Set generous timeouts
            self.driver.set_page_load_timeout(90)  # 90 seconds for page load
            self.driver.implicitly_wait(20)  # 20 seconds implicit wait
            
            self.wait = WebDriverWait(self.driver, 30)  # 30 seconds explicit wait
            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Chrome WebDriver: {e}")
            raise
    
    def navigate_to_page(self):
        """Navigate to the IEX page with patience"""
        try:
            logger.info(f"Navigating to {self.url}")
            logger.info("This may take up to 90 seconds...")
            
            self.driver.get(self.url)
            
            # Wait for page to be ready
            logger.info("Waiting for page to be ready...")
            time.sleep(10)  # Give it time to load
            
            # Check if we're on the right page
            current_url = self.driver.current_url
            page_title = self.driver.title
            
            logger.info(f"Page title: {page_title}")
            logger.info(f"Current URL: {current_url}")
            
            if "iexindia.com" not in current_url.lower():
                raise Exception(f"Unexpected URL: {current_url}")
            
            # Take screenshot for verification
            self.driver.save_screenshot("page_loaded.png")
            logger.info("Page loaded successfully, screenshot saved")
            
        except Exception as e:
            logger.error(f"Failed to navigate to page: {e}")
            try:
                self.driver.save_screenshot("navigation_failed.png")
            except:
                pass
            raise
    
    def wait_and_click(self, xpath, description, timeout=30):
        """Wait for element and click it with multiple strategies"""
        try:
            logger.info(f"Looking for {description}...")
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )

            # Scroll to element and wait
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
            time.sleep(2)

            # Try multiple click strategies
            strategies = [
                ("Regular click", lambda: element.click()),
                ("JavaScript click", lambda: self.driver.execute_script("arguments[0].click();", element)),
                ("Action click", lambda: self.driver.execute_script("arguments[0].dispatchEvent(new MouseEvent('click', {bubbles: true}));", element))
            ]

            for strategy_name, click_func in strategies:
                try:
                    logger.info(f"Trying {strategy_name} for {description}")
                    click_func()
                    logger.info(f"Successfully clicked {description} using {strategy_name}")
                    time.sleep(3)  # Wait after click
                    return True
                except Exception as e:
                    logger.warning(f"{strategy_name} failed for {description}: {e}")
                    continue

            logger.error(f"All click strategies failed for {description}")
            return False

        except TimeoutException:
            logger.error(f"Timeout waiting for {description}")
            return False
        except Exception as e:
            logger.error(f"Error finding {description}: {e}")
            return False
    
    def wait_and_type(self, xpath, text, description, timeout=30):
        """Wait for input field and type text"""
        try:
            logger.info(f"Looking for {description}...")
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
            
            # Clear and type
            element.clear()
            element.send_keys(text)
            logger.info(f"Successfully entered '{text}' in {description}")
            time.sleep(1)
            
            return True
            
        except TimeoutException:
            logger.error(f"Timeout waiting for {description}")
            return False
        except Exception as e:
            logger.error(f"Error typing in {description}: {e}")
            return False
    
    def select_date_range(self, start_date, end_date):
        """Select custom date range step by step"""
        try:
            logger.info(f"Selecting date range: {start_date} to {end_date}")

            # Take screenshot before starting
            self.driver.save_screenshot("before_date_selection.png")

            # Scroll to top to avoid header overlap
            self.driver.execute_script("window.scrollTo(0, 200);")
            time.sleep(2)

            # Step 1: Click date selector
            logger.info("Step 1: Clicking date selector...")
            if not self.wait_and_click('//*[@id="heyy"]/div/div/div/div', "date selector"):
                # Try alternative approach - look for any dropdown or select element
                logger.info("Trying alternative date selector approach...")
                try:
                    # Look for elements with "Today" or similar text
                    dropdowns = self.driver.find_elements(By.TAG_NAME, "select")
                    buttons = self.driver.find_elements(By.CSS_SELECTOR, "div[role='button']")

                    logger.info(f"Found {len(dropdowns)} dropdowns and {len(buttons)} clickable divs")

                    # Try clicking elements that might be date selectors
                    for i, button in enumerate(buttons[:5]):  # Try first 5 buttons
                        try:
                            if "today" in button.text.lower() or "date" in button.get_attribute("class").lower():
                                logger.info(f"Trying button {i}: {button.text}")
                                self.driver.execute_script("arguments[0].click();", button)
                                time.sleep(2)
                                break
                        except:
                            continue
                    else:
                        return False
                except Exception as e:
                    logger.error(f"Alternative approach failed: {e}")
                    return False

            # Step 2: Click custom range option
            logger.info("Step 2: Clicking custom range option...")
            if not self.wait_and_click('//*[@id=":r3:"]/li[6]', "custom range option"):
                # Try to find any list item with "range" or "select" text
                logger.info("Trying alternative custom range approach...")
                try:
                    list_items = self.driver.find_elements(By.TAG_NAME, "li")
                    for item in list_items:
                        text = item.text.lower()
                        if "select" in text and "range" in text:
                            logger.info(f"Found range option: {item.text}")
                            self.driver.execute_script("arguments[0].click();", item)
                            time.sleep(2)
                            break
                    else:
                        return False
                except Exception as e:
                    logger.error(f"Alternative range selection failed: {e}")
                    return False

            # Step 3: Enter start date
            logger.info("Step 3: Entering start date...")
            if not self.wait_and_type('//*[@id=":r6:"]', start_date, "start date field"):
                # Try to find any date input field
                logger.info("Trying alternative start date approach...")
                try:
                    date_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='text'], input[placeholder*='DD-MM-YYYY']")
                    if date_inputs:
                        date_inputs[0].clear()
                        date_inputs[0].send_keys(start_date)
                        logger.info(f"Entered start date in alternative field")
                        time.sleep(1)
                    else:
                        return False
                except Exception as e:
                    logger.error(f"Alternative start date entry failed: {e}")
                    return False

            # Step 4: Enter end date
            logger.info("Step 4: Entering end date...")
            if not self.wait_and_type('//*[@id=":r8:"]', end_date, "end date field"):
                # Try to find the second date input field
                logger.info("Trying alternative end date approach...")
                try:
                    date_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='text'], input[placeholder*='DD-MM-YYYY']")
                    if len(date_inputs) >= 2:
                        date_inputs[1].clear()
                        date_inputs[1].send_keys(end_date)
                        logger.info(f"Entered end date in alternative field")
                        time.sleep(1)
                    else:
                        return False
                except Exception as e:
                    logger.error(f"Alternative end date entry failed: {e}")
                    return False

            # Step 5: Click update button
            logger.info("Step 5: Clicking update button...")
            if not self.wait_and_click('/html/body/div[1]/div[4]/section/div[1]/div[1]/div[2]/button', "update button"):
                # Try to find any button with "update" text
                logger.info("Trying alternative update button approach...")
                try:
                    buttons = self.driver.find_elements(By.TAG_NAME, "button")
                    for button in buttons:
                        if "update" in button.text.lower():
                            logger.info(f"Found update button: {button.text}")
                            self.driver.execute_script("arguments[0].click();", button)
                            time.sleep(2)
                            break
                    else:
                        return False
                except Exception as e:
                    logger.error(f"Alternative update button failed: {e}")
                    return False

            # Wait for data to load
            logger.info("Waiting for data to load...")
            time.sleep(15)  # Increased wait time

            # Take screenshot after selection
            self.driver.save_screenshot("after_date_selection.png")

            return True

        except Exception as e:
            logger.error(f"Error in date selection: {e}")
            self.driver.save_screenshot("date_selection_error.png")
            return False
    
    def extract_table_data(self):
        """Extract data from the table"""
        try:
            logger.info("Looking for data table...")
            
            # Wait for table to be present
            table = self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            logger.info("Table found")
            
            # Extract headers
            headers = []
            try:
                header_elements = table.find_elements(By.TAG_NAME, "th")
                for header in header_elements:
                    headers.append(header.text.strip())
                logger.info(f"Found {len(headers)} headers")
            except Exception as e:
                logger.warning(f"Could not extract headers: {e}")
                headers = ["Date", "Hour", "Time Block", "Purchase Bid (MW)", "Sell Bid (MW)", "MCV (MW)", "Final Scheduled Volume (MW)", "MCP (Rs/MWh)"]
            
            # Extract rows
            rows = []
            try:
                row_elements = table.find_elements(By.TAG_NAME, "tr")
                logger.info(f"Found {len(row_elements)} total rows")
                
                for i, row in enumerate(row_elements[1:], 1):  # Skip header row
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if cells:
                        row_data = []
                        for cell in cells:
                            row_data.append(cell.text.strip())
                        if row_data:  # Only add non-empty rows
                            rows.append(row_data)
                
                logger.info(f"Extracted {len(rows)} data rows")
                
            except Exception as e:
                logger.error(f"Error extracting table rows: {e}")
                return None, None
            
            return headers, rows
            
        except TimeoutException:
            logger.error("Timeout waiting for table")
            return None, None
        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            return None, None
    
    def save_to_csv(self, headers, rows, filename=None):
        """Save data to CSV"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"iex_data_{timestamp}.csv"
            
            # Create DataFrame
            df = pd.DataFrame(rows, columns=headers)
            
            # Save to CSV
            df.to_csv(filename, index=False)
            logger.info(f"Data saved to {filename}")
            logger.info(f"Saved {len(rows)} rows with {len(headers)} columns")
            
            return filename
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
    
    def extract_data(self, start_date, end_date):
        """Main extraction method"""
        try:
            # Navigate to page
            self.navigate_to_page()
            
            # Select date range
            if not self.select_date_range(start_date, end_date):
                raise Exception("Failed to select date range")
            
            # Extract table data
            headers, rows = self.extract_table_data()
            if headers is None or rows is None:
                raise Exception("Failed to extract table data")
            
            # Save to CSV
            filename = self.save_to_csv(headers, rows)
            if filename is None:
                raise Exception("Failed to save data")
            
            return filename
            
        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise
        finally:
            self.close()
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            logger.info("Browser closed")

def main():
    """Main function for testing"""
    import sys
    
    # Get dates from command line or use defaults
    if len(sys.argv) >= 3:
        start_date = sys.argv[1]
        end_date = sys.argv[2]
    else:
        # Use today's date
        today = datetime.now().strftime("%d-%m-%Y")
        start_date = today
        end_date = today
        print(f"No dates provided, using today: {today}")
    
    print(f"Extracting data from {start_date} to {end_date}")
    print("Running in visible mode for debugging...")
    
    try:
        extractor = SimpleIEXExtractor(headless=False)  # Visible for debugging
        filename = extractor.extract_data(start_date, end_date)
        print(f"Success! Data saved to: {filename}")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Check the screenshots and logs for more details.")

if __name__ == "__main__":
    main()

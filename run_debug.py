#!/usr/bin/env python3
"""
Simple script to run IEX extractor in non-headless mode for debugging
"""

import sys
from datetime import datetime
from iex_data_extractor import IEXDataExtractor
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    # Default to today's date if no arguments provided
    if len(sys.argv) >= 3:
        start_date = sys.argv[1]
        end_date = sys.argv[2]
    else:
        today = datetime.now().strftime("%d-%m-%Y")
        start_date = today
        end_date = today
        print(f"No dates provided, using today: {today}")
    
    print(f"Extracting data from {start_date} to {end_date}")
    print("Running in NON-HEADLESS mode for debugging...")
    print("You can watch the browser automation in action.")
    
    # Create extractor with headless=False
    extractor = IEXDataExtractor(headless=False)
    
    try:
        output_file = extractor.extract_data(start_date, end_date)
        print(f"Success! Data saved to: {output_file}")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Check the browser window for more details.")
        input("Press Enter to close...")
    
    finally:
        # Keep browser open for inspection
        input("Press Enter to close the browser...")

if __name__ == "__main__":
    main()

# IEX India Market Data Extractor - Project Summary

## 🎯 Project Overview

This project provides a complete automated solution for extracting market snapshot data from the IEX (Indian Energy Exchange) day-ahead market website. The solution handles custom date range selection, data extraction, and CSV export with comprehensive error handling and logging.

## 📁 Project Structure

```
IEX_PercentageBifurcation/
├── iex_data_extractor.py      # Main extractor class and CLI
├── config.py                  # Configuration settings
├── example_usage.py           # Usage examples
├── test_setup.py             # Setup validation tests
├── requirements.txt          # Python dependencies
├── run_extractor.sh          # Unix/Linux/macOS runner script
├── run_extractor.bat         # Windows runner script
├── README.md                 # Detailed documentation
└── PROJECT_SUMMARY.md        # This summary file
```

## 🚀 Quick Start

### 1. Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Test setup
python3 test_setup.py
```

### 2. Basic Usage
```bash
# Extract data for a date range
python3 iex_data_extractor.py --start-date "01-08-2024" --end-date "07-08-2024"

# Or use the interactive script
./run_extractor.sh  # Unix/Linux/macOS
run_extractor.bat   # Windows
```

### 3. Python API
```python
from iex_data_extractor import IEXDataExtractor

extractor = IEXDataExtractor(headless=True)
output_file = extractor.extract_data("01-08-2024", "07-08-2024")
print(f"Data saved to: {output_file}")
```

## 🔧 Key Features

### ✅ Automated Web Interaction
- **Custom Date Range Selection**: Automatically selects date ranges using the website's interface
- **Element Detection**: Uses XPath selectors to interact with specific web elements
- **Error Recovery**: Handles timeouts and element not found errors gracefully

### ✅ Data Extraction
- **Table Parsing**: Extracts complete market data tables
- **Data Validation**: Validates extracted data structure and content
- **Multiple Formats**: Supports CSV export (extensible to Excel, JSON)

### ✅ Robust Configuration
- **Configurable Settings**: All parameters configurable via `config.py`
- **Alternative Selectors**: Backup selectors in case primary ones fail
- **Rate Limiting**: Respectful delays between requests

### ✅ User-Friendly Interface
- **Command Line Interface**: Easy-to-use CLI with argument parsing
- **Interactive Scripts**: Menu-driven scripts for common scenarios
- **Cross-Platform**: Works on Windows, macOS, and Linux

### ✅ Error Handling & Logging
- **Comprehensive Logging**: Detailed logs for debugging and monitoring
- **Error Screenshots**: Captures screenshots on errors for debugging
- **Graceful Failures**: Proper cleanup and error reporting

## 📊 Data Output

The extracted CSV contains the following columns:
- **Date**: Trading date
- **Hour**: Hour of the day (1-24)
- **Time Block**: 15-minute intervals
- **Purchase Bid (MW)**: Purchase bid volume
- **Sell Bid (MW)**: Sell bid volume
- **MCV (MW)**: Market Cleared Volume (Unconstrained)
- **Final Scheduled Volume (MW)**: Volume adjusted for real-time curtailment
- **MCP (Rs/MWh)**: Market Clearing Price (Unconstrained)

## 🛠 Technical Implementation

### Web Automation
- **Selenium WebDriver**: Chrome-based automation
- **WebDriver Manager**: Automatic ChromeDriver management
- **Headless Operation**: Can run without GUI

### Data Processing
- **Pandas**: Data manipulation and CSV export
- **Date Validation**: Robust date format validation
- **Error Recovery**: Multiple retry mechanisms

### Architecture
- **Modular Design**: Separate concerns (extraction, configuration, utilities)
- **Extensible**: Easy to add new features or export formats
- **Maintainable**: Clear code structure with comprehensive documentation

## 🔍 Web Elements Targeted

The script interacts with these specific elements on the IEX website:

| Element | XPath | Purpose |
|---------|-------|---------|
| Date Selector | `//*[@id="heyy"]/div/div/div/div` | Opens date range picker |
| Custom Range Option | `//*[@id=":r3:"]/li[6]` | Selects custom date range |
| Start Date Input | `//*[@id=":r6:"]` | Enter start date |
| End Date Input | `//*[@id=":r8:"]` | Enter end date |
| Update Button | `/html/body/div[1]/div[4]/section/div[1]/div[1]/div[2]/button` | Triggers data update |

## 📋 Usage Scenarios

### 1. Daily Data Collection
```bash
# Extract today's data
python3 iex_data_extractor.py --start-date "$(date +%d-%m-%Y)" --end-date "$(date +%d-%m-%Y)"
```

### 2. Historical Analysis
```bash
# Extract last month's data
python3 iex_data_extractor.py --start-date "01-07-2024" --end-date "31-07-2024"
```

### 3. Automated Reporting
```python
# Scheduled extraction with custom processing
from iex_data_extractor import IEXDataExtractor
import pandas as pd

extractor = IEXDataExtractor()
data_file = extractor.extract_data("01-08-2024", "07-08-2024")

# Process data
df = pd.read_csv(data_file)
# Add your analysis here...
```

## 🔒 Compliance & Ethics

- **Respectful Scraping**: Implements delays and rate limiting
- **Error Handling**: Graceful failure without overwhelming the server
- **Terms of Service**: Users responsible for compliance with IEX terms
- **Educational Purpose**: Designed for research and analysis

## 🚨 Troubleshooting

### Common Issues
1. **ChromeDriver Issues**: Automatically handled by WebDriver Manager
2. **Element Not Found**: Check if website structure changed
3. **Timeout Errors**: Increase wait times in configuration
4. **Date Format**: Ensure DD-MM-YYYY format

### Debug Mode
```bash
# Run with visible browser for debugging
python3 iex_data_extractor.py --start-date "01-08-2024" --end-date "07-08-2024" --headless False
```

## 📈 Future Enhancements

### Potential Improvements
- **Multiple Market Types**: Support for other IEX market data
- **Real-time Monitoring**: Live data extraction capabilities
- **Data Analysis**: Built-in analysis and visualization
- **API Integration**: REST API for programmatic access
- **Database Storage**: Direct database integration
- **Notification System**: Alerts for data availability

### Extensibility Points
- **Export Formats**: Easy to add Excel, JSON, database exports
- **Data Sources**: Framework can be adapted for other energy exchanges
- **Processing Pipeline**: Add data transformation and analysis steps
- **Scheduling**: Integration with cron jobs or task schedulers

## 📞 Support

For issues or questions:
1. Check the troubleshooting section in README.md
2. Run the test setup: `python3 test_setup.py`
3. Review the logs for detailed error information
4. Ensure all dependencies are properly installed

## 📄 License & Disclaimer

This tool is for educational and research purposes. Users are responsible for:
- Compliance with IEX India's terms of service
- Respectful usage that doesn't overload the website
- Proper attribution when using extracted data
- Following applicable laws and regulations

The tool is not affiliated with IEX India and comes with no warranties or guarantees.

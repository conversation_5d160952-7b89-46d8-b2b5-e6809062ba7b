#!/usr/bin/env python3
"""
Corrected IEX Data Extractor with proper element selectors

Based on analysis, the correct elements are:
- Date selector: div with text "Today" (not the "heyy" element which is interval selector)
- Update button: button with text "Update Report"
"""

import time
import logging
import pandas as pd
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CorrectedIEXExtractor:
    def __init__(self, headless=False):
        self.url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Set up Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            self.driver.set_page_load_timeout(90)
            self.driver.implicitly_wait(20)
            
            self.wait = WebDriverWait(self.driver, 30)
            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Chrome WebDriver: {e}")
            raise
    
    def navigate_to_page(self):
        """Navigate to the IEX page"""
        try:
            logger.info(f"Navigating to {self.url}")
            self.driver.get(self.url)
            time.sleep(10)
            
            logger.info(f"Page title: {self.driver.title}")
            logger.info(f"Current URL: {self.driver.current_url}")
            
            # Scroll down to avoid header overlap
            self.driver.execute_script("window.scrollTo(0, 400);")
            time.sleep(2)
            
            self.driver.save_screenshot("corrected_page_loaded.png")
            logger.info("Page loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to navigate to page: {e}")
            raise
    
    def click_date_selector(self):
        """Click the correct date selector (the one with 'Today' text)"""
        try:
            logger.info("Looking for date selector with 'Today' text...")
            
            # Find the element with "Today" text that's a dropdown
            date_selector = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'MuiSelect-select') and text()='Today']"))
            )
            
            logger.info(f"Found date selector at location: {date_selector.location}")
            
            # Scroll to element and click
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", date_selector)
            time.sleep(2)
            
            # Try multiple click strategies
            try:
                date_selector.click()
                logger.info("Successfully clicked date selector with regular click")
            except Exception as e:
                logger.warning(f"Regular click failed: {e}, trying JavaScript click")
                self.driver.execute_script("arguments[0].click();", date_selector)
                logger.info("Successfully clicked date selector with JavaScript click")
            
            time.sleep(3)
            self.driver.save_screenshot("after_date_selector_click.png")
            return True
            
        except TimeoutException:
            logger.error("Timeout waiting for date selector")
            return False
        except Exception as e:
            logger.error(f"Error clicking date selector: {e}")
            return False
    
    def select_custom_range(self):
        """Select the custom range option from the dropdown"""
        try:
            logger.info("Looking for custom range option...")
            
            # Wait for dropdown menu to appear and look for range option
            # Try multiple selectors for the range option
            range_selectors = [
                "//li[@data-value='SELECT_RANGE']",
                "//li[contains(text(), 'Select Range')]",
                "//li[contains(text(), 'SELECT_RANGE')]",
                "//li[contains(text(), 'range') or contains(text(), 'Range')]",
                "//li[position()=6]",  # 6th item as mentioned in original XPath
                "//li[last()]"  # Last item
            ]
            
            for i, selector in enumerate(range_selectors):
                try:
                    logger.info(f"Trying selector {i+1}: {selector}")
                    range_option = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    
                    logger.info(f"Found range option: '{range_option.text}'")
                    range_option.click()
                    logger.info("Successfully clicked range option")
                    time.sleep(3)
                    self.driver.save_screenshot("after_range_selection.png")
                    return True
                    
                except TimeoutException:
                    logger.warning(f"Selector {i+1} timed out")
                    continue
                except Exception as e:
                    logger.warning(f"Selector {i+1} failed: {e}")
                    continue
            
            logger.error("All range selectors failed")
            return False
            
        except Exception as e:
            logger.error(f"Error selecting custom range: {e}")
            return False
    
    def enter_dates(self, start_date, end_date):
        """Enter start and end dates"""
        try:
            logger.info(f"Entering dates: {start_date} to {end_date}")
            
            # Wait for date input fields to appear
            time.sleep(3)
            
            # Look for date input fields
            date_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='text'], input[placeholder*='DD'], input[placeholder*='MM'], input[placeholder*='YYYY']")
            
            if len(date_inputs) < 2:
                logger.error(f"Expected 2 date inputs, found {len(date_inputs)}")
                return False
            
            # Enter start date
            logger.info("Entering start date...")
            start_input = date_inputs[0]
            start_input.clear()
            start_input.send_keys(start_date)
            time.sleep(1)
            
            # Enter end date
            logger.info("Entering end date...")
            end_input = date_inputs[1]
            end_input.clear()
            end_input.send_keys(end_date)
            time.sleep(1)
            
            logger.info("Dates entered successfully")
            self.driver.save_screenshot("after_date_entry.png")
            return True
            
        except Exception as e:
            logger.error(f"Error entering dates: {e}")
            return False
    
    def click_update_button(self):
        """Click the Update Report button"""
        try:
            logger.info("Looking for Update Report button...")

            # Wait a bit for the button to become enabled after date entry
            time.sleep(3)

            # Try multiple approaches to find the update button
            button_selectors = [
                "//button[contains(text(), 'Update Report')]",
                "//button[contains(@class, 'MuiButton') and contains(text(), 'Update')]",
                "//button[@type='button' and contains(text(), 'Update')]",
                "//button[contains(@class, 'mui-1ry5qw7')]",  # From our analysis
                "/html/body/div[1]/div[4]/section/div[1]/div[1]/div[2]/button"  # Original XPath
            ]

            for i, selector in enumerate(button_selectors):
                try:
                    logger.info(f"Trying button selector {i+1}: {selector}")

                    # First check if button exists
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    logger.info(f"Found {len(buttons)} buttons with this selector")

                    for j, button in enumerate(buttons):
                        logger.info(f"  Button {j}: text='{button.text}', enabled={button.is_enabled()}, displayed={button.is_displayed()}")
                        logger.info(f"    Location: {button.location}, Class: {button.get_attribute('class')}")

                    if buttons:
                        button = buttons[0]  # Use first button found

                        # Scroll to button
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                        time.sleep(2)

                        # Check if button is enabled
                        if not button.is_enabled():
                            logger.warning(f"Button is disabled, waiting for it to become enabled...")
                            # Wait up to 10 seconds for button to become enabled
                            for wait_count in range(10):
                                time.sleep(1)
                                if button.is_enabled():
                                    logger.info(f"Button became enabled after {wait_count + 1} seconds")
                                    break
                            else:
                                logger.warning("Button remained disabled, trying to click anyway...")

                        # Try clicking
                        try:
                            button.click()
                            logger.info("Successfully clicked update button with regular click")
                        except Exception as e:
                            logger.warning(f"Regular click failed: {e}, trying JavaScript click")
                            self.driver.execute_script("arguments[0].click();", button)
                            logger.info("Successfully clicked update button with JavaScript click")

                        # Wait for data to load
                        logger.info("Waiting for data to load...")
                        time.sleep(15)

                        self.driver.save_screenshot("after_update_click.png")
                        return True

                except Exception as e:
                    logger.warning(f"Button selector {i+1} failed: {e}")
                    continue

            # If all selectors failed, try to find any button with "Update" text
            logger.info("All specific selectors failed, looking for any button with 'Update' text...")
            all_buttons = self.driver.find_elements(By.TAG_NAME, "button")
            logger.info(f"Found {len(all_buttons)} total buttons on page")

            for i, button in enumerate(all_buttons):
                if button.is_displayed() and "update" in button.text.lower():
                    logger.info(f"Found potential update button {i}: '{button.text}' at {button.location}")
                    try:
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                        time.sleep(1)
                        self.driver.execute_script("arguments[0].click();", button)
                        logger.info("Successfully clicked button with JavaScript")
                        time.sleep(15)
                        self.driver.save_screenshot("after_generic_update_click.png")
                        return True
                    except Exception as e:
                        logger.warning(f"Failed to click button {i}: {e}")
                        continue

            logger.error("Could not find or click any update button")
            return False

        except Exception as e:
            logger.error(f"Error in click_update_button: {e}")
            return False
    
    def extract_table_data(self):
        """Extract data from the table"""
        try:
            logger.info("Looking for data table...")
            
            # Wait for table to be present
            table = self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            logger.info("Table found")
            
            # Extract headers
            headers = []
            try:
                header_elements = table.find_elements(By.TAG_NAME, "th")
                for header in header_elements:
                    headers.append(header.text.strip())
                logger.info(f"Found {len(headers)} headers: {headers}")
            except Exception as e:
                logger.warning(f"Could not extract headers: {e}")
                headers = ["Date", "Hour", "Time Block", "Purchase Bid (MW)", "Sell Bid (MW)", "MCV (MW)", "Final Scheduled Volume (MW)", "MCP (Rs/MWh)"]
            
            # Extract rows
            rows = []
            try:
                row_elements = table.find_elements(By.TAG_NAME, "tr")
                logger.info(f"Found {len(row_elements)} total rows")
                
                for i, row in enumerate(row_elements[1:], 1):  # Skip header row
                    cells = row.find_elements(By.TAG_NAME, "td")
                    if cells:
                        row_data = []
                        for cell in cells:
                            row_data.append(cell.text.strip())
                        if row_data:  # Only add non-empty rows
                            rows.append(row_data)
                
                logger.info(f"Extracted {len(rows)} data rows")
                
            except Exception as e:
                logger.error(f"Error extracting table rows: {e}")
                return None, None
            
            return headers, rows
            
        except TimeoutException:
            logger.error("Timeout waiting for table")
            return None, None
        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            return None, None
    
    def save_to_csv(self, headers, rows, filename=None):
        """Save data to CSV"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"iex_data_corrected_{timestamp}.csv"
            
            # Create DataFrame
            df = pd.DataFrame(rows, columns=headers)
            
            # Save to CSV
            df.to_csv(filename, index=False)
            logger.info(f"Data saved to {filename}")
            logger.info(f"Saved {len(rows)} rows with {len(headers)} columns")
            
            return filename
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
    
    def extract_data(self, start_date, end_date):
        """Main extraction method"""
        try:
            # Step 1: Navigate to page
            self.navigate_to_page()
            
            # Step 2: Click date selector
            if not self.click_date_selector():
                raise Exception("Failed to click date selector")
            
            # Step 3: Select custom range
            if not self.select_custom_range():
                raise Exception("Failed to select custom range")
            
            # Step 4: Enter dates
            if not self.enter_dates(start_date, end_date):
                raise Exception("Failed to enter dates")
            
            # Step 5: Click update button
            if not self.click_update_button():
                raise Exception("Failed to click update button")
            
            # Step 6: Extract table data
            headers, rows = self.extract_table_data()
            if headers is None or rows is None:
                raise Exception("Failed to extract table data")
            
            # Step 7: Save to CSV
            filename = self.save_to_csv(headers, rows)
            if filename is None:
                raise Exception("Failed to save data")
            
            return filename
            
        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise
        finally:
            self.close()
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            logger.info("Browser closed")

def main():
    """Main function for testing"""
    import sys
    
    # Get dates from command line or use defaults
    if len(sys.argv) >= 3:
        start_date = sys.argv[1]
        end_date = sys.argv[2]
    else:
        # Use today's date
        today = datetime.now().strftime("%d-%m-%Y")
        start_date = today
        end_date = today
        print(f"No dates provided, using today: {today}")
    
    print(f"Extracting data from {start_date} to {end_date}")
    print("Running with corrected element selectors...")
    
    try:
        extractor = CorrectedIEXExtractor(headless=False)  # Visible for debugging
        filename = extractor.extract_data(start_date, end_date)
        print(f"Success! Data saved to: {filename}")
        
    except Exception as e:
        print(f"Error: {e}")
        print("Check the screenshots and logs for more details.")

if __name__ == "__main__":
    main()

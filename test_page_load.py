#!/usr/bin/env python3
"""
Simple test script to check if we can load the IEX page
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_page_load():
    """Test loading the IEX page"""
    
    # Setup Chrome options
    chrome_options = Options()
    # Don't run headless for debugging
    # chrome_options.add_argument("--headless")
    
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    driver = None
    try:
        # Initialize driver
        logger.info("Initializing Chrome driver...")
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Set timeouts
        driver.set_page_load_timeout(60)
        driver.implicitly_wait(10)
        
        logger.info("Driver initialized successfully")
        
        # Navigate to page
        url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        logger.info(f"Navigating to: {url}")
        
        driver.get(url)
        logger.info("Page load command sent")
        
        # Wait a bit
        time.sleep(10)
        
        # Get page info
        title = driver.title
        current_url = driver.current_url
        
        logger.info(f"Page title: {title}")
        logger.info(f"Current URL: {current_url}")
        
        # Take screenshot
        driver.save_screenshot("page_load_test.png")
        logger.info("Screenshot saved as page_load_test.png")
        
        # Look for some basic elements
        try:
            body = driver.find_element(By.TAG_NAME, "body")
            logger.info(f"Body element found, text length: {len(body.text)}")
        except Exception as e:
            logger.error(f"Could not find body element: {e}")
        
        # Look for buttons
        buttons = driver.find_elements(By.TAG_NAME, "button")
        logger.info(f"Found {len(buttons)} buttons on the page")
        
        # Look for inputs
        inputs = driver.find_elements(By.TAG_NAME, "input")
        logger.info(f"Found {len(inputs)} input fields on the page")
        
        # Look for tables
        tables = driver.find_elements(By.TAG_NAME, "table")
        logger.info(f"Found {len(tables)} tables on the page")
        
        # Look for the specific elements we need
        logger.info("Looking for specific elements...")
        
        # Try to find the date selector
        try:
            date_selector = driver.find_element(By.XPATH, '//*[@id="heyy"]/div/div/div/div')
            logger.info("✓ Date selector found!")
        except Exception as e:
            logger.warning(f"✗ Date selector not found: {e}")
        
        # Try to find any element with id "heyy"
        try:
            heyy_element = driver.find_element(By.ID, "heyy")
            logger.info("✓ Element with ID 'heyy' found!")
        except Exception as e:
            logger.warning(f"✗ Element with ID 'heyy' not found: {e}")
        
        # Save page source for inspection
        with open("page_source.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        logger.info("Page source saved as page_source.html")
        
        logger.info("Test completed successfully!")
        
        # Keep browser open for manual inspection
        input("Press Enter to close the browser...")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        if driver:
            try:
                driver.save_screenshot("error_screenshot.png")
                logger.info("Error screenshot saved")
            except:
                pass
    
    finally:
        if driver:
            driver.quit()
            logger.info("Driver closed")

if __name__ == "__main__":
    test_page_load()

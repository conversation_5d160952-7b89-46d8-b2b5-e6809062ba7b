#!/usr/bin/env python3
"""
Configuration file for IEX Data Extractor

This file contains all the configurable parameters for the IEX data extractor.
Modify these values to customize the behavior of the extractor.
"""

# Website Configuration
IEX_URL = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"

# XPath Selectors for Web Elements
XPATHS = {
    # Date range selector button
    "date_selector": '//*[@id="heyy"]/div/div/div/div',
    
    # Custom date range option in dropdown
    "custom_range_option": '//*[@id=":r3:"]/li[6]',
    
    # Start date input field
    "start_date_input": '//*[@id=":r6:"]',
    
    # End date input field
    "end_date_input": '//*[@id=":r8:"]',
    
    # Update report button
    "update_button": '/html/body/div[1]/div[4]/section/div[1]/div[1]/div[2]/button',
    
    # Data table
    "data_table": "table"
}

# Alternative selectors (backup in case primary selectors fail)
ALTERNATIVE_SELECTORS = {
    "date_selector": [
        "button[aria-label*='date']",
        ".date-selector",
        "[data-testid='date-selector']"
    ],
    "custom_range_option": [
        "li[data-value='SELECT_RANGE']",
        "li:contains('Select Range')",
        ".custom-range-option"
    ],
    "start_date_input": [
        "input[placeholder*='DD-MM-YYYY']:first",
        ".start-date-input",
        "[data-testid='start-date']"
    ],
    "end_date_input": [
        "input[placeholder*='DD-MM-YYYY']:last",
        ".end-date-input",
        "[data-testid='end-date']"
    ],
    "update_button": [
        "button:contains('Update Report')",
        ".update-button",
        "[data-testid='update-button']"
    ]
}

# WebDriver Configuration
WEBDRIVER_CONFIG = {
    # Maximum wait time for elements (seconds)
    "max_wait_time": 20,
    
    # Page load timeout (seconds)
    "page_load_timeout": 30,
    
    # Implicit wait time (seconds)
    "implicit_wait": 10,
    
    # Chrome options
    "chrome_options": [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--window-size=1920,1080",
        "--disable-blink-features=AutomationControlled",
        "--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    ],
    
    # Retry configuration
    "max_retries": 3,
    "retry_delay": 2  # seconds
}

# Data Processing Configuration
DATA_CONFIG = {
    # Default output filename pattern
    "default_filename_pattern": "iex_market_data_{timestamp}.csv",
    
    # Date format for input validation
    "date_format": "%d-%m-%Y",
    
    # CSV export settings
    "csv_settings": {
        "index": False,
        "encoding": "utf-8",
        "float_format": "%.2f"
    },
    
    # Data validation rules
    "validation_rules": {
        "min_rows": 1,
        "required_columns": [
            "Date", "Hour", "Time Block", "Purchase Bid (MW)", 
            "Sell Bid (MW)", "MCV (MW)", "Final Scheduled Volume (MW)", 
            "MCP (Rs/MWh)"
        ]
    }
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "log_to_file": False,
    "log_filename": "iex_extractor.log"
}

# Error Handling Configuration
ERROR_CONFIG = {
    # Whether to take screenshots on errors
    "screenshot_on_error": True,
    
    # Screenshot directory
    "screenshot_dir": "error_screenshots",
    
    # Whether to save page source on errors
    "save_page_source": True,
    
    # Page source directory
    "page_source_dir": "error_page_sources"
}

# Rate Limiting Configuration
RATE_LIMIT_CONFIG = {
    # Delay between requests (seconds)
    "request_delay": 1,
    
    # Delay after clicking elements (seconds)
    "click_delay": 2,
    
    # Delay after page navigation (seconds)
    "navigation_delay": 3
}

# Export Configuration
EXPORT_CONFIG = {
    # Supported export formats
    "supported_formats": ["csv", "excel", "json"],
    
    # Default export format
    "default_format": "csv",
    
    # Excel specific settings
    "excel_settings": {
        "sheet_name": "IEX_Market_Data",
        "index": False
    },
    
    # JSON specific settings
    "json_settings": {
        "orient": "records",
        "indent": 2
    }
}

# Development/Debug Configuration
DEBUG_CONFIG = {
    # Whether to run in debug mode
    "debug_mode": False,
    
    # Whether to keep browser open after extraction
    "keep_browser_open": False,
    
    # Whether to print detailed logs
    "verbose_logging": False,
    
    # Whether to validate extracted data
    "validate_data": True
}

def get_config():
    """
    Get the complete configuration dictionary
    
    Returns:
        dict: Complete configuration
    """
    return {
        "url": IEX_URL,
        "xpaths": XPATHS,
        "alternative_selectors": ALTERNATIVE_SELECTORS,
        "webdriver": WEBDRIVER_CONFIG,
        "data": DATA_CONFIG,
        "logging": LOGGING_CONFIG,
        "error": ERROR_CONFIG,
        "rate_limit": RATE_LIMIT_CONFIG,
        "export": EXPORT_CONFIG,
        "debug": DEBUG_CONFIG
    }

def update_config(**kwargs):
    """
    Update configuration values
    
    Args:
        **kwargs: Configuration values to update
    """
    config = get_config()
    for key, value in kwargs.items():
        if key in config:
            if isinstance(config[key], dict) and isinstance(value, dict):
                config[key].update(value)
            else:
                config[key] = value
    return config

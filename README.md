# IEX India Market Data Extractor

This project provides an automated solution to extract market snapshot data from the IEX (Indian Energy Exchange) day-ahead market website. The extractor can handle custom date range selection and exports data to CSV format.

## Features

- **Automated Data Extraction**: Scrapes market data from IEX India's official website
- **Custom Date Range Selection**: Allows you to specify start and end dates for data extraction
- **CSV Export**: Automatically saves extracted data to CSV files
- **Headless Operation**: Can run without opening a browser window
- **Error Handling**: Comprehensive error handling and logging
- **Automatic Driver Management**: Automatically downloads and manages ChromeDriver

## Prerequisites

- Python 3.7 or higher
- Google Chrome browser installed
- Internet connection

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Command Line Interface

The main script can be used from the command line:

```bash
python iex_data_extractor.py --start-date "01-08-2024" --end-date "07-08-2024"
```

#### Command Line Arguments

- `--start-date`: Start date in DD-MM-YYYY format (required)
- `--end-date`: End date in DD-MM-YYYY format (required)
- `--output`: Output CSV filename (optional)
- `--headless`: Run in headless mode (default: True)

#### Examples

```bash
# Extract data for a specific date range
python iex_data_extractor.py --start-date "01-08-2024" --end-date "07-08-2024"

# Extract data with custom output filename
python iex_data_extractor.py --start-date "01-08-2024" --end-date "07-08-2024" --output "my_data.csv"

# Run with browser visible (non-headless mode)
python iex_data_extractor.py --start-date "01-08-2024" --end-date "07-08-2024" --headless False
```

### Python API

You can also use the extractor as a Python module:

```python
from iex_data_extractor import IEXDataExtractor

# Create extractor instance
extractor = IEXDataExtractor(headless=True)

# Extract data for date range
output_file = extractor.extract_data("01-08-2024", "07-08-2024")
print(f"Data saved to: {output_file}")
```

### Example Usage Script

Run the example script to see different usage scenarios:

```bash
python example_usage.py
```

This script demonstrates:
- Extracting today's data
- Extracting last week's data
- Extracting custom date ranges

## Data Format

The extracted CSV file contains the following columns:

- **Date**: Trading date
- **Hour**: Hour of the day (1-24)
- **Time Block**: 15-minute time blocks (e.g., "00:00 - 00:15")
- **Purchase Bid (MW)**: Purchase bid volume in MW
- **Sell Bid (MW)**: Sell bid volume in MW
- **MCV (MW)**: Market Cleared Volume (Unconstrained) in MW
- **Final Scheduled Volume (MW)**: Volume adjusted for real-time curtailment in MW
- **MCP (Rs/MWh)**: Market Clearing Price (Unconstrained) in Rs/MWh

## Technical Details

### Web Elements Used

The script interacts with the following web elements on the IEX website:

- **Date Range Selector**: `//*[@id="heyy"]/div/div/div/div`
- **Custom Range Option**: `//*[@id=":r3:"]/li[6]`
- **Start Date Input**: `//*[@id=":r6:"]`
- **End Date Input**: `//*[@id=":r8:"]`
- **Update Report Button**: `/html/body/div[1]/div[4]/section/div[1]/div[1]/div[2]/button`

### Error Handling

The script includes comprehensive error handling for:
- WebDriver initialization failures
- Element not found errors
- Timeout exceptions
- Data extraction errors
- File saving errors

### Logging

All operations are logged with appropriate log levels:
- INFO: Normal operation messages
- ERROR: Error messages with details
- DEBUG: Detailed debugging information (when enabled)

## Troubleshooting

### Common Issues

1. **ChromeDriver Issues**: The script automatically downloads ChromeDriver, but ensure Chrome browser is installed
2. **Timeout Errors**: Increase the wait time if the website is slow to load
3. **Element Not Found**: The website structure may have changed; check if XPath selectors need updating
4. **Date Format**: Ensure dates are in DD-MM-YYYY format

### Debug Mode

To run in debug mode with visible browser:

```bash
python iex_data_extractor.py --start-date "01-08-2024" --end-date "07-08-2024" --headless False
```

## Dependencies

- `selenium==4.15.2`: Web automation framework
- `pandas==2.1.3`: Data manipulation and CSV export
- `webdriver-manager==4.0.1`: Automatic ChromeDriver management

## License

This project is for educational and research purposes. Please ensure compliance with IEX India's terms of service when using this tool.

## Disclaimer

This tool is not affiliated with IEX India. Users are responsible for ensuring their usage complies with the website's terms of service and applicable laws. The extracted data should be used responsibly and in accordance with IEX India's data usage policies.

## Support

For issues or questions, please check the troubleshooting section above or review the code comments for detailed explanations of each function.

#!/usr/bin/env python3
"""
Debug version of IEX Data Extractor

This script runs in non-headless mode by default for debugging purposes.
It includes additional logging and error handling to help identify issues.
"""

import time
import logging
from datetime import datetime
from selenium.webdriver.common.by import By
from iex_data_extractor import IEXDataExtractor

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('debug_extractor.log')
    ]
)
logger = logging.getLogger(__name__)

class DebugIEXExtractor(IEXDataExtractor):
    """Debug version of IEX extractor with enhanced logging and error handling"""
    
    def __init__(self, headless=False):
        """Initialize with non-headless mode by default"""
        super().__init__(headless=headless)
    
    def debug_page_info(self):
        """Print debug information about the current page"""
        try:
            logger.info("=== PAGE DEBUG INFO ===")
            logger.info(f"Current URL: {self.driver.current_url}")
            logger.info(f"Page Title: {self.driver.title}")
            logger.info(f"Window Size: {self.driver.get_window_size()}")
            
            # Check if page is loaded
            ready_state = self.driver.execute_script("return document.readyState")
            logger.info(f"Document Ready State: {ready_state}")
            
            # Count common elements
            divs = len(self.driver.find_elements(By.TAG_NAME, "div"))
            buttons = len(self.driver.find_elements(By.TAG_NAME, "button"))
            inputs = len(self.driver.find_elements(By.TAG_NAME, "input"))
            tables = len(self.driver.find_elements(By.TAG_NAME, "table"))
            
            logger.info(f"Elements found - Divs: {divs}, Buttons: {buttons}, Inputs: {inputs}, Tables: {tables}")
            
            # Look for specific IDs mentioned in the XPaths
            specific_elements = [
                "heyy",
                ":r3:",
                ":r6:",
                ":r8:"
            ]
            
            for element_id in specific_elements:
                try:
                    element = self.driver.find_element(By.ID, element_id)
                    logger.info(f"Found element with ID '{element_id}': {element.tag_name}")
                except:
                    logger.warning(f"Element with ID '{element_id}' not found")
            
            logger.info("=== END PAGE DEBUG INFO ===")
            
        except Exception as e:
            logger.error(f"Error getting page debug info: {e}")
    
    def wait_for_user_input(self, message="Press Enter to continue..."):
        """Wait for user input during debugging"""
        input(f"\n{message}")
    
    def navigate_to_page(self):
        """Navigate with debug information"""
        logger.info("Starting navigation with debug mode...")
        super().navigate_to_page()
        
        # Add debug information
        self.debug_page_info()
        
        # Wait for user to inspect the page
        self.wait_for_user_input("Page loaded. Inspect the browser window and press Enter to continue...")
    
    def select_custom_date_range(self, start_date, end_date):
        """Select date range with debug pauses"""
        logger.info("Starting date range selection with debug mode...")
        
        try:
            # Debug before starting
            self.debug_page_info()
            self.wait_for_user_input("About to start date selection. Press Enter to continue...")
            
            # Call parent method
            super().select_custom_date_range(start_date, end_date)
            
            # Debug after completion
            self.debug_page_info()
            self.wait_for_user_input("Date selection completed. Press Enter to continue...")
            
        except Exception as e:
            logger.error(f"Error in debug date selection: {e}")
            self.debug_page_info()
            self.wait_for_user_input("Error occurred. Check the browser window and press Enter to continue...")
            raise
    
    def extract_table_data(self):
        """Extract table data with debug information"""
        logger.info("Starting table data extraction with debug mode...")
        
        try:
            # Debug before extraction
            self.debug_page_info()
            self.wait_for_user_input("About to extract table data. Press Enter to continue...")
            
            # Call parent method
            headers, rows = super().extract_table_data()
            
            logger.info(f"Extracted {len(headers)} headers: {headers}")
            logger.info(f"Extracted {len(rows)} rows")
            if rows:
                logger.info(f"First row sample: {rows[0]}")
            
            return headers, rows
            
        except Exception as e:
            logger.error(f"Error in debug table extraction: {e}")
            self.debug_page_info()
            self.wait_for_user_input("Error occurred during table extraction. Check the browser and press Enter...")
            raise

def debug_extract_data(start_date, end_date):
    """
    Debug version of data extraction
    
    Args:
        start_date (str): Start date in DD-MM-YYYY format
        end_date (str): End date in DD-MM-YYYY format
    """
    logger.info("Starting debug data extraction...")
    logger.info(f"Date range: {start_date} to {end_date}")
    
    extractor = DebugIEXExtractor(headless=False)
    
    try:
        # Step by step extraction with user interaction
        logger.info("Step 1: Navigating to page...")
        extractor.navigate_to_page()
        
        logger.info("Step 2: Selecting date range...")
        extractor.select_custom_date_range(start_date, end_date)
        
        logger.info("Step 3: Extracting table data...")
        headers, rows = extractor.extract_table_data()
        
        logger.info("Step 4: Saving data...")
        filename = extractor.save_to_csv(headers, rows, f"debug_data_{start_date.replace('-', '_')}_to_{end_date.replace('-', '_')}.csv")
        
        logger.info(f"Debug extraction completed successfully!")
        logger.info(f"Data saved to: {filename}")
        
        return filename
        
    except Exception as e:
        logger.error(f"Debug extraction failed: {e}")
        input("Press Enter to close the browser...")
        raise
    finally:
        input("Press Enter to close the browser...")
        extractor.close()

def main():
    """Main debug function"""
    print("IEX Data Extractor - DEBUG MODE")
    print("=" * 40)
    print("This version runs with visible browser for debugging.")
    print("You'll be prompted at each step to inspect the browser.")
    print()
    
    # Get dates from user
    start_date = input("Enter start date (DD-MM-YYYY): ").strip()
    end_date = input("Enter end date (DD-MM-YYYY): ").strip()
    
    if not start_date or not end_date:
        print("Using default dates for testing...")
        today = datetime.now()
        start_date = today.strftime("%d-%m-%Y")
        end_date = today.strftime("%d-%m-%Y")
        print(f"Using dates: {start_date} to {end_date}")
    
    try:
        debug_extract_data(start_date, end_date)
        print("\nDebug extraction completed successfully!")
        
    except Exception as e:
        print(f"\nDebug extraction failed: {e}")
        print("Check the debug_extractor.log file for detailed logs.")
        print("Screenshots should be saved in the current directory.")

if __name__ == "__main__":
    main()

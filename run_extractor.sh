#!/bin/bash

# IEX Data Extractor - Easy Run Script
# This script provides an easy way to run the IEX data extractor with common scenarios

echo "IEX India Market Data Extractor"
echo "==============================="
echo ""

# Function to validate date format
validate_date() {
    if [[ $1 =~ ^[0-9]{2}-[0-9]{2}-[0-9]{4}$ ]]; then
        return 0
    else
        return 1
    fi
}

# Function to get today's date in DD-MM-YYYY format
get_today() {
    date +"%d-%m-%Y"
}

# Function to get date N days ago
get_date_days_ago() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        date -v-${1}d +"%d-%m-%Y"
    else
        # Linux
        date -d "${1} days ago" +"%d-%m-%Y"
    fi
}

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed or not in PATH"
    exit 1
fi

# Check if required files exist
if [ ! -f "iex_data_extractor.py" ]; then
    echo "Error: iex_data_extractor.py not found in current directory"
    exit 1
fi

if [ ! -f "requirements.txt" ]; then
    echo "Error: requirements.txt not found in current directory"
    exit 1
fi

# Install dependencies if needed
echo "Checking dependencies..."
python3 -c "import selenium, pandas, webdriver_manager" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing required dependencies..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
fi

echo "Dependencies OK!"
echo ""

# Menu options
echo "Select an option:"
echo "1. Extract today's data"
echo "2. Extract yesterday's data"
echo "3. Extract last 7 days data"
echo "4. Extract custom date range"
echo "5. Run test setup"
echo "6. Exit"
echo ""

read -p "Enter your choice (1-6): " choice

case $choice in
    1)
        echo "Extracting today's data..."
        TODAY=$(get_today)
        python3 iex_data_extractor.py --start-date "$TODAY" --end-date "$TODAY" --output "iex_data_today.csv"
        ;;
    2)
        echo "Extracting yesterday's data..."
        YESTERDAY=$(get_date_days_ago 1)
        python3 iex_data_extractor.py --start-date "$YESTERDAY" --end-date "$YESTERDAY" --output "iex_data_yesterday.csv"
        ;;
    3)
        echo "Extracting last 7 days data..."
        START_DATE=$(get_date_days_ago 7)
        END_DATE=$(get_today)
        python3 iex_data_extractor.py --start-date "$START_DATE" --end-date "$END_DATE" --output "iex_data_last_7_days.csv"
        ;;
    4)
        echo "Enter custom date range:"
        read -p "Start date (DD-MM-YYYY): " start_date
        read -p "End date (DD-MM-YYYY): " end_date
        
        if ! validate_date "$start_date"; then
            echo "Error: Invalid start date format. Use DD-MM-YYYY"
            exit 1
        fi
        
        if ! validate_date "$end_date"; then
            echo "Error: Invalid end date format. Use DD-MM-YYYY"
            exit 1
        fi
        
        read -p "Output filename (optional, press Enter for auto-generated): " output_file
        
        if [ -z "$output_file" ]; then
            python3 iex_data_extractor.py --start-date "$start_date" --end-date "$end_date"
        else
            python3 iex_data_extractor.py --start-date "$start_date" --end-date "$end_date" --output "$output_file"
        fi
        ;;
    5)
        echo "Running test setup..."
        python3 test_setup.py
        ;;
    6)
        echo "Goodbye!"
        exit 0
        ;;
    *)
        echo "Invalid choice. Please select 1-6."
        exit 1
        ;;
esac

echo ""
echo "Operation completed!"

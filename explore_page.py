#!/usr/bin/env python3
"""
Page Explorer - to understand the current page structure after clicking date selector
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def explore_page():
    """Explore the page structure step by step"""
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.set_page_load_timeout(90)
    
    try:
        # Navigate to page
        url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        logger.info(f"Navigating to {url}")
        driver.get(url)
        time.sleep(10)
        
        logger.info(f"Page title: {driver.title}")
        driver.save_screenshot("step1_page_loaded.png")
        
        # Scroll down a bit to avoid header
        driver.execute_script("window.scrollTo(0, 200);")
        time.sleep(2)
        
        # Look for the date selector
        logger.info("Looking for date selector...")
        try:
            date_selector = driver.find_element(By.XPATH, '//*[@id="heyy"]/div/div/div/div')
            logger.info("Found date selector!")
            
            # Click it
            driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", date_selector)
            time.sleep(2)
            driver.execute_script("arguments[0].click();", date_selector)
            logger.info("Clicked date selector")
            time.sleep(5)
            
            driver.save_screenshot("step2_after_date_click.png")
            
            # Now explore what's available
            logger.info("Exploring available options...")
            
            # Look for dropdown menus
            dropdowns = driver.find_elements(By.CSS_SELECTOR, "ul[role='listbox'], div[role='listbox'], .MuiMenu-list, .MuiList-root")
            logger.info(f"Found {len(dropdowns)} dropdown/list elements")
            
            for i, dropdown in enumerate(dropdowns):
                logger.info(f"Dropdown {i}: {dropdown.get_attribute('class')}")
                items = dropdown.find_elements(By.TAG_NAME, "li")
                logger.info(f"  - Contains {len(items)} items")
                for j, item in enumerate(items[:10]):  # Show first 10 items
                    logger.info(f"    Item {j}: '{item.text}' (value: {item.get_attribute('data-value')})")
            
            # Look for any visible list items
            all_lis = driver.find_elements(By.TAG_NAME, "li")
            visible_lis = [li for li in all_lis if li.is_displayed()]
            logger.info(f"Found {len(visible_lis)} visible list items")
            
            for i, li in enumerate(visible_lis[:20]):  # Show first 20
                logger.info(f"Visible LI {i}: '{li.text}' (class: {li.get_attribute('class')})")
            
            # Look for elements with specific text
            logger.info("Looking for elements with 'range' or 'select' text...")
            elements_with_range = driver.find_elements(By.XPATH, "//*[contains(text(), 'range') or contains(text(), 'Range') or contains(text(), 'SELECT') or contains(text(), 'Select')]")
            for i, elem in enumerate(elements_with_range):
                if elem.is_displayed():
                    logger.info(f"Range element {i}: '{elem.text}' (tag: {elem.tag_name}, class: {elem.get_attribute('class')})")
            
            # Save page source for detailed analysis
            with open("page_source_after_click.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            logger.info("Page source saved to page_source_after_click.html")
            
            # Wait for user to inspect
            input("Press Enter to continue exploring...")
            
            # Try to find and click a range option
            logger.info("Trying to find and click range option...")
            
            # Try different approaches
            approaches = [
                ("XPath with text", "//li[contains(text(), 'Select Range') or contains(text(), 'SELECT_RANGE')]"),
                ("Data value", "//li[@data-value='SELECT_RANGE']"),
                ("Generic range", "//li[contains(text(), 'range') or contains(text(), 'Range')]"),
                ("6th item", "//li[6]"),
                ("Last item", "//li[last()]")
            ]
            
            for approach_name, xpath in approaches:
                try:
                    logger.info(f"Trying {approach_name}: {xpath}")
                    elements = driver.find_elements(By.XPATH, xpath)
                    logger.info(f"Found {len(elements)} elements")
                    
                    for i, elem in enumerate(elements):
                        if elem.is_displayed():
                            logger.info(f"  Element {i}: '{elem.text}' - clicking...")
                            driver.execute_script("arguments[0].click();", elem)
                            time.sleep(3)
                            driver.save_screenshot(f"step3_after_{approach_name.replace(' ', '_')}_click.png")
                            
                            # Check if date inputs appeared
                            date_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='text'], input[placeholder*='DD'], input[placeholder*='MM'], input[placeholder*='YYYY']")
                            logger.info(f"Found {len(date_inputs)} date input fields after clicking")
                            
                            if date_inputs:
                                logger.info("SUCCESS! Date inputs appeared")
                                for j, inp in enumerate(date_inputs):
                                    logger.info(f"  Input {j}: placeholder='{inp.get_attribute('placeholder')}', id='{inp.get_attribute('id')}'")
                                return
                            break
                    
                except Exception as e:
                    logger.warning(f"{approach_name} failed: {e}")
            
        except Exception as e:
            logger.error(f"Error with date selector: {e}")
        
        input("Press Enter to close browser...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    explore_page()

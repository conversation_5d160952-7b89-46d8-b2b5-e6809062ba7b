#!/usr/bin/env python3
"""
Debug script to examine what date input elements are actually available
after selecting custom range
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_date_inputs():
    """Debug the date input elements after selecting custom range"""
    
    # Setup Chrome
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--window-size=1920,1080")
    
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.set_page_load_timeout(90)
    
    try:
        # Navigate to page
        url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        logger.info(f"Navigating to {url}")
        driver.get(url)
        time.sleep(10)
        
        # Scroll down
        driver.execute_script("window.scrollTo(0, 400);")
        time.sleep(2)
        
        # Click date selector
        logger.info("Clicking date selector...")
        date_selector = WebDriverWait(driver, 30).until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'MuiSelect-select') and text()='Today']"))
        )
        date_selector.click()
        time.sleep(3)
        
        # Select custom range
        logger.info("Selecting custom range...")
        range_option = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, "//li[@data-value='SELECT_RANGE']"))
        )
        range_option.click()
        time.sleep(5)
        
        driver.save_screenshot("debug_after_range_selection.png")
        
        # Now examine all input elements
        logger.info("=== EXAMINING ALL INPUT ELEMENTS ===")
        
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        logger.info(f"Found {len(all_inputs)} total input elements")
        
        for i, inp in enumerate(all_inputs):
            if inp.is_displayed():
                logger.info(f"Input {i}:")
                logger.info(f"  ID: '{inp.get_attribute('id')}'")
                logger.info(f"  Type: '{inp.get_attribute('type')}'")
                logger.info(f"  Placeholder: '{inp.get_attribute('placeholder')}'")
                logger.info(f"  Value: '{inp.get_attribute('value')}'")
                logger.info(f"  Class: '{inp.get_attribute('class')}'")
                logger.info(f"  Location: {inp.location}")
                logger.info(f"  Size: {inp.size}")
                logger.info(f"  Enabled: {inp.is_enabled()}")
                logger.info("")
        
        # Look specifically for date-related inputs
        logger.info("=== LOOKING FOR DATE-RELATED INPUTS ===")
        
        date_selectors = [
            "input[placeholder*='DD']",
            "input[placeholder*='MM']", 
            "input[placeholder*='YYYY']",
            "input[placeholder*='DD-MM-YYYY']",
            "input[type='text']",
            "input[type='date']",
            "input[id*='r']",  # IDs like :r6:, :r8:
            "input[class*='MuiInputBase-input']"
        ]
        
        for selector in date_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    logger.info(f"Selector '{selector}' found {len(elements)} elements:")
                    for j, elem in enumerate(elements):
                        if elem.is_displayed():
                            logger.info(f"  Element {j}: id='{elem.get_attribute('id')}', placeholder='{elem.get_attribute('placeholder')}'")
            except Exception as e:
                logger.warning(f"Selector '{selector}' failed: {e}")
        
        # Try the specific IDs mentioned by user
        logger.info("=== CHECKING SPECIFIC IDs ===")
        specific_ids = [":r6:", ":r8:", ":r0:", ":r2:", ":r4:"]
        
        for element_id in specific_ids:
            try:
                element = driver.find_element(By.ID, element_id)
                logger.info(f"Found element with ID '{element_id}':")
                logger.info(f"  Displayed: {element.is_displayed()}")
                logger.info(f"  Enabled: {element.is_enabled()}")
                logger.info(f"  Placeholder: '{element.get_attribute('placeholder')}'")
                logger.info(f"  Value: '{element.get_attribute('value')}'")
                logger.info(f"  Location: {element.location}")
            except Exception as e:
                logger.info(f"Element with ID '{element_id}' not found: {e}")
        
        # Save page source for detailed analysis
        with open("debug_page_source_after_range.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        logger.info("Page source saved for analysis")
        
        # Interactive testing
        logger.info("=== INTERACTIVE TESTING ===")
        input("Press Enter to start interactive testing...")
        
        # Find the most likely date input candidates
        date_candidates = driver.find_elements(By.CSS_SELECTOR, "input[placeholder*='DD-MM-YYYY']")
        
        if not date_candidates:
            date_candidates = driver.find_elements(By.CSS_SELECTOR, "input[type='text']")
            date_candidates = [inp for inp in date_candidates if inp.is_displayed()]
        
        logger.info(f"Found {len(date_candidates)} date input candidates")
        
        for i, candidate in enumerate(date_candidates[:4]):  # Test first 4
            try:
                logger.info(f"Testing candidate {i}: ID='{candidate.get_attribute('id')}'")
                
                # Highlight the element
                driver.execute_script("arguments[0].style.border='3px solid red';", candidate)
                time.sleep(1)
                
                # Try to enter a test date
                test_date = "01-01-2024"
                candidate.click()
                time.sleep(0.5)
                candidate.clear()
                time.sleep(0.5)
                candidate.send_keys(test_date)
                time.sleep(1)
                
                result_value = candidate.get_attribute('value')
                logger.info(f"  Test result: '{result_value}'")
                
                # Remove highlight
                driver.execute_script("arguments[0].style.border='';", candidate)
                
                if test_date in result_value:
                    logger.info(f"  SUCCESS! This element accepts date input")
                else:
                    logger.info(f"  Failed to enter date properly")
                
                time.sleep(2)
                
            except Exception as e:
                logger.warning(f"Testing candidate {i} failed: {e}")
        
        input("Press Enter to close browser...")
        
    finally:
        driver.quit()

if __name__ == "__main__":
    debug_date_inputs()

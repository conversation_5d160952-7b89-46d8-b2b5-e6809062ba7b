#!/usr/bin/env python3
"""
IEX Data Extractor - Simple and Direct Approach

This version uses a more direct approach to handle the MUI date inputs
by simulating actual user typing behavior.
"""

import time
import logging
import pandas as pd
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IEXExtractor:
    def __init__(self, headless=False):
        self.url = "https://www.iexindia.com/market-data/day-ahead-market/market-snapshot"
        self.driver = None
        self.wait = None
        self.setup_driver(headless)
    
    def setup_driver(self, headless=False):
        """Set up Chrome WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            self.driver.set_page_load_timeout(90)
            self.driver.implicitly_wait(20)
            
            self.wait = WebDriverWait(self.driver, 30)
            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Chrome WebDriver: {e}")
            raise
    
    def navigate_to_page(self):
        """Navigate to the IEX page"""
        try:
            logger.info(f"Navigating to {self.url}")
            self.driver.get(self.url)
            time.sleep(10)
            
            # Scroll down to avoid header overlap
            self.driver.execute_script("window.scrollTo(0, 400);")
            time.sleep(2)
            
            logger.info("Page loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to navigate to page: {e}")
            raise
    
    def click_date_selector(self):
        """Click the date selector"""
        try:
            logger.info("Looking for date selector...")
            
            date_selector = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'MuiSelect-select') and text()='Today']"))
            )
            
            date_selector.click()
            logger.info("Successfully clicked date selector")
            time.sleep(3)
            
            return True
            
        except Exception as e:
            logger.error(f"Error clicking date selector: {e}")
            return False
    
    def select_custom_range(self):
        """Select the custom range option"""
        try:
            logger.info("Looking for custom range option...")
            
            range_option = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//li[@data-value='SELECT_RANGE']"))
            )
            
            range_option.click()
            logger.info("Successfully clicked range option")
            time.sleep(5)
            
            return True
            
        except Exception as e:
            logger.error(f"Error selecting custom range: {e}")
            return False
    
    def type_date_slowly(self, element, date_value, field_name):
        """Type date character by character like a real user"""
        try:
            logger.info(f"Typing {field_name}: {date_value}")
            
            # Click to focus
            element.click()
            time.sleep(0.5)
            
            # Clear field by selecting all and deleting
            element.send_keys(Keys.CONTROL + "a")
            time.sleep(0.2)
            element.send_keys(Keys.DELETE)
            time.sleep(0.5)
            
            # Type each character slowly
            for i, char in enumerate(date_value):
                element.send_keys(char)
                time.sleep(0.2)  # Slow typing to trigger validation
                
                # Check if field is being cleared during typing
                current_value = element.get_attribute('value')
                logger.info(f"After typing '{char}' (pos {i+1}): '{current_value}'")
                
                # If field gets cleared, stop and report
                if len(current_value) < i:
                    logger.warning(f"Field was cleared while typing at position {i+1}")
                    return False
            
            # Wait a moment after typing
            time.sleep(1)
            
            # Check final value
            final_value = element.get_attribute('value')
            logger.info(f"Final value for {field_name}: '{final_value}'")
            
            # Don't blur immediately - let the field keep focus
            return date_value in final_value
            
        except Exception as e:
            logger.error(f"Error typing {field_name}: {e}")
            return False
    
    def enter_dates_carefully(self, start_date, end_date):
        """Enter dates with careful focus management"""
        try:
            logger.info(f"Entering dates carefully: {start_date} to {end_date}")
            
            # Wait for date input fields to appear
            time.sleep(5)
            
            # Find both input elements
            start_element = self.driver.find_element(By.ID, ":r0:")
            end_element = self.driver.find_element(By.ID, ":r2:")
            
            logger.info("Found both date input elements")
            
            # Enter start date
            logger.info("Entering start date...")
            start_success = self.type_date_slowly(start_element, start_date, "start date")
            
            if not start_success:
                logger.warning("Start date entry failed")
                return False
            
            # Small delay before moving to end date
            time.sleep(1)
            
            # Enter end date
            logger.info("Entering end date...")
            end_success = self.type_date_slowly(end_element, end_date, "end date")
            
            if not end_success:
                logger.warning("End date entry failed")
                return False
            
            # Wait and verify both dates are still there
            time.sleep(2)
            
            final_start = start_element.get_attribute('value')
            final_end = end_element.get_attribute('value')
            
            logger.info(f"Final verification - Start: '{final_start}', End: '{final_end}'")
            
            # Check if update button is enabled
            try:
                update_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Update Report')]")
                button_enabled = update_button.is_enabled()
                logger.info(f"Update button enabled: {button_enabled}")
                
                if button_enabled:
                    logger.info("SUCCESS! Update button is now enabled!")
                    return True
                else:
                    logger.warning("Dates entered but update button still disabled")
                    
            except Exception as e:
                logger.warning(f"Could not check update button: {e}")
            
            # Return success if dates are present
            return (start_date in final_start) and (end_date in final_end)
            
        except Exception as e:
            logger.error(f"Error entering dates carefully: {e}")
            return False
    
    def click_update_button(self):
        """Click the Update Report button"""
        try:
            logger.info("Clicking Update Report button...")
            
            update_button = self.driver.find_element(By.XPATH, "//button[contains(text(), 'Update Report')]")
            
            if update_button.is_enabled():
                update_button.click()
                logger.info("Successfully clicked enabled update button")
            else:
                logger.warning("Button is disabled, clicking anyway with JavaScript")
                self.driver.execute_script("arguments[0].click();", update_button)
                logger.info("Clicked disabled button with JavaScript")
            
            # Wait for data to load
            time.sleep(15)
            
            return True
            
        except Exception as e:
            logger.error(f"Error clicking update button: {e}")
            return False
    
    def extract_table_data(self):
        """Extract data from the table"""
        try:
            logger.info("Looking for data table...")
            
            table = self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "table")))
            logger.info("Table found")
            
            # Extract headers
            headers = []
            header_elements = table.find_elements(By.TAG_NAME, "th")
            for header in header_elements:
                headers.append(header.text.strip())
            
            # Extract rows
            rows = []
            row_elements = table.find_elements(By.TAG_NAME, "tr")[1:]  # Skip header
            
            for row in row_elements:
                cells = row.find_elements(By.TAG_NAME, "td")
                if cells:
                    row_data = [cell.text.strip() for cell in cells]
                    if row_data:
                        rows.append(row_data)
            
            logger.info(f"Extracted {len(rows)} rows with {len(headers)} columns")
            return headers, rows
            
        except Exception as e:
            logger.error(f"Error extracting table data: {e}")
            return None, None
    
    def save_to_csv(self, headers, rows, filename=None):
        """Save data to CSV"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"iex_data_{timestamp}.csv"
            
            df = pd.DataFrame(rows, columns=headers)
            df.to_csv(filename, index=False)
            
            logger.info(f"Data saved to {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None
    
    def extract_data(self, start_date, end_date):
        """Main extraction method"""
        try:
            self.navigate_to_page()
            
            if not self.click_date_selector():
                raise Exception("Failed to click date selector")
            
            if not self.select_custom_range():
                raise Exception("Failed to select custom range")
            
            if not self.enter_dates_carefully(start_date, end_date):
                raise Exception("Failed to enter dates")
            
            if not self.click_update_button():
                raise Exception("Failed to click update button")
            
            headers, rows = self.extract_table_data()
            if headers is None or rows is None:
                raise Exception("Failed to extract table data")
            
            filename = self.save_to_csv(headers, rows)
            if filename is None:
                raise Exception("Failed to save data")
            
            return filename
            
        except Exception as e:
            logger.error(f"Data extraction failed: {e}")
            raise
        finally:
            self.close()
    
    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()
            logger.info("Browser closed")

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) >= 3:
        start_date = sys.argv[1]
        end_date = sys.argv[2]
    else:
        today = datetime.now().strftime("%d-%m-%Y")
        start_date = today
        end_date = today
        print(f"No dates provided, using today: {today}")
    
    print(f"Extracting data from {start_date} to {end_date}")
    print("Using careful typing approach...")
    
    try:
        extractor = IEXExtractor(headless=False)
        filename = extractor.extract_data(start_date, end_date)
        print(f"Success! Data saved to: {filename}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
